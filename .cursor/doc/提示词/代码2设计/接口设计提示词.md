你是一名资深后端架构师，请根据我提供的 Java 接口代码，反向生成一份接口详细设计文档，输出要求如下：
📌 输出要求：
	- 输出格式为 Markdown（.md）
	- 文件名为当前接口类名，例如 UserRegisterController.md
	- 保存路径为项目根目录下的 .知识库/文档/上上一层包名/上一层包名/ 目录
	- 图示必须使用 PlantUML 语法（不要使用 Mermaid）
📘 接口详细设计文档应包含以下内容结构（请完整输出）：
	1. 接口名称：接口的功能名称（中文）
	2. 接口说明：接口用途、业务背景、使用场景
	3. 接口类型：HTTP / Dubbo / WebSocket 等
	4. 接口地址或方法签名：
		- HTTP 接口：请求方式 + URL
		- Dubbo 接口：接口类和方法签名
	5. 请求参数表（以表格形式展示）：
		- 中文名
		- 英文名（字段名）
		- 类型（如 String、int、List）
		- 是否必填（是 / 否）
		- 示例值
		- 字段说明
	6. 响应参数表：
		- 同样格式，列出接口响应字段及含义
	7. 返回码说明：返回码说明表，列出返回码、说明、备注
	8. 关键业务逻辑说明：用自然语言描述核心处理逻辑与判断分支，尽可能详细
	9. 流程图（使用 PlantUML 语法绘制）：
		- @startuml ... @enduml 包裹流程图
	10. 时序图（使用 PlantUML 语法）：
		- 展示接口调用链：前端 → 网关 → 控制器 → Service → DB / 缓存
	11. 异常处理机制：主要异常场景及处理方式
	12. 调用的公共模块或外部依赖：
		- 模块名称
		- 功能简述
	13.	幂等性与安全性说明：是否幂等、鉴权、限流、验签等
	14.	备注与风险点（可选）：注意事项、边界处理、特殊逻辑说明
✏️ 注意事项：
	- 所有图示必须使用 PlantUML 标准语法
	- Markdown 文档结构清晰，适合归档和设计评审使用
	- 文件最终保存在项目根目录下 .知识库/文档/上上一层包名/上一层包名/类名.md