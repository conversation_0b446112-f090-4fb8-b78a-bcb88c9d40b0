<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://code.alibabatech.com/schema/dubbo
        http://code.alibabatech.com/schema/dubbo/dubbo.xsd
        ">
    <dubbo:application name="${dubbo.application.name}"/>



    <dubbo:registry id="acc-center-server" protocol="zookeeper" address="${dubbo.registries.acc-center-server.address}" check="false" register="false" default="false" register-mode="interface"/>
    <dubbo:reference id="queryCustSensitiveInfoFacade" registry="acc-center-server" interface="com.howbuy.acccenter.facade.query.sensitive.custinfo.QueryCustSensitiveInfoFacade" check="false" retries="0" timeout="1200000"/>
    <dubbo:reference id="queryOuterAcctLoginBindFacade" registry="acc-center-server" interface="com.howbuy.acccenter.facade.query.queryouteracctloginbind.QueryOuterAcctLoginBindFacade" check="false" retries="0" timeout="1200000" />
    <dubbo:reference id="queryWechatAcctBindFacade" registry="acc-center-server" interface="com.howbuy.acccenter.facade.query.querywechatbindinfo.QueryWechatAcctBindFacade" check="false" retries="0" timeout="1200000" />


    <dubbo:registry id="crm-core-server" protocol="zookeeper" address="${dubbo.registries.crm-core-server.address}" check="false" register="false" default="false" register-mode="instance"/>
    <dubbo:reference id="queryCustconstantInfoService" registry="crm-core-server" interface="com.howbuy.crm.conscust.service.QueryCustconstantInfoService" check="false" retries="0" timeout="1200000" />

    <dubbo:registry id="crm-nt-server" protocol="zookeeper" address="${dubbo.registries.crm-nt-server.address}" check="false" register="false" default="false" register-mode="all"/>
    <dubbo:reference id="queryConscustListService" registry="crm-nt-server" interface="com.howbuy.crm.nt.conscust.service.QueryConscustListService" check="false" retries="0" timeout="1200000" />

    <dubbo:registry id="message-remote" protocol="zookeeper" address="${dubbo.registries.message-remote.address}" check="false" register="false" default="false" register-mode="all"/>
    <dubbo:reference id="companySendService" registry="message-remote" interface="com.howbuy.cc.message.send.company.CompanySendService" check="false" retries="0" timeout="1200000" />
    <dubbo:reference id="SendEmailWithAttachSupportEncryService" registry="message-remote" interface="com.howbuy.cc.message.email.SendEmailWithAttachSupportEncryService" check="false" retries="0" timeout="1200000" />


</beans>