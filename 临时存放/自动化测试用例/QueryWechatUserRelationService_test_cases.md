# `QueryWechatUserRelationService` 接口测试用例

## 1. 接口概述

- **接口名称:** `QueryWechatUserRelationService`
- **接口路径:** `com.howbuy.crm.wechat.client.producer.userrelation.QueryWechatUserRelationService.execute(QueryWechatUserRelationRequest)`
- **功能描述:** 根据客户的一账通号（HbOneNo）查询该客户与所有关联的企业微信理财师的关系列表。

## 2. 依赖数据表范围

为了给接口准备真实有效的测试数据，需要确保以下 **3个表** 中的数据是相互关联且逻辑正确的。

| 表名 | 用途 | 关键字段 | 关联逻辑 |
| :--- | :--- | :--- | :--- |
| `cm_wechat_cust_info` | 存储企微客户的基本信息。 | `HBONE_NO`, `EXTERNAL_USER_ID` | 通过 `HBONE_NO` 找到客户的企微 `EXTERNAL_USER_ID`。 |
| `cm_wechat_cust_relation` | 存储企微客户和理财师的关联关系。 | `EXTERNAL_USER_ID`, `conscode`, `status` | 连接客户和理财师，并记录关系状态（如是否为好友）。 |
| `cm_wechat_emp` | 存储企微员工（理财师）的信息。 | `conscode`, `emp_id`, `emp_name` | 提供理财师的详细信息。 |

**数据准备核心思路:**
一个有效的测试数据至少需要包含：一个客户 (`cm_wechat_cust_info`)，他/她至少关联了一位理财师 (`cm_wechat_cust_relation` -> `cm_wechat_emp`)，并且关联关系是有效的。

## 3. 输入参数 (`QueryWechatUserRelationRequest`)

| 参数名 | 类型 | 是否必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `hbOneNo` | `String` | 是 | 客户的唯一标识 (HbOneNo)，不能为空。 |

## 4. 输出结果 (`Response<QueryWechatUserRelationResponse>`)

- **成功:** 返回 `Response.ok(QueryWechatUserRelationResponse)`，其中 `QueryWechatUserRelationResponse` 包含客户与理财师的关系列表。
- **失败:** 返回带有错误码和错误信息的 `Response` 对象。

## 5. 测试用例

### 5.1. 正常场景测试

| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-N-001** | **单一关联关系查询** | 验证当一个客户仅关联一个理财师时，接口能正确返回这唯一的关联关系。 | 数据库中存在一个 `hbOneNo` 对应一个有效的理财师关联关系。 | `hbOneNo`: `9200000103` | | 1. `Response.code` 为 `200`。<br>2. `Response.data` 不为 `null`。<br>3. `Response.data.getRelationList()` 返回一个包含1条记录的列表，且信息正确。 |
| **TC-N-002** | **多个关联关系查询** | 验证当一个客户关联了多个理财师时，接口能正确返回所有关联关系。 | 数据库中存在一个 `hbOneNo` 对应多个有效的理财师关联关系。 | `hbOneNo`: `9200001009` | | 1. `Response.code` 为 `200`。<br>2. `Response.data` 不为 `null`。<br>3. `Response.data.getRelationList()` 返回一个包含多条记录的列表。 |
| **TC-N-003** | **无关联关系查询** | 验证当客户存在但没有任何理财师关联时，接口返回空列表。 | 数据库中存在该 `hbOneNo` 的客户，但该客户没有任何理财师关联关系。 | `hbOneNo`: `9200001010` | | 1. `Response.code` 为 `200`。<br>2. `Response.data` 不为 `null`。<br>3. `Response.data.getRelationList()` 返回一个空列表 `[]`。 |
| **TC-N-004** | **客户不存在查询** | 验证查询一个不存在的客户时，接口能正常处理并返回空列表。 | 数据库中不存在 `hbOneNo` 对应的客户信息。 | `hbOneNo`: `C_NON_EXIST` | | 1. `Response.code` 为 `200`。<br>2. `Response.data` 不为 `null`。<br>3. `Response.data.getRelationList()` 返回一个空列表 `[]`。 |
| **TC-N-005** | **关联关系为无效状态** | 验证当客户的关联关系状态为无效（如已删除）时，接口不会返回这些无效关系。 | 客户与理财师的关联关系 `status` 为非正常状态（例如，已删除）。 | `hbOneNo`: `9200001011` | | 1. `Response.code` 为 `200`。<br>2. `Response.data` 不为 `null`。<br>3. `Response.data.getRelationList()` 返回一个空列表 `[]` (假设业务逻辑会过滤无效关系)。 |

### 5.2. 异常及边界场景测试

| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-E-001** | **`hbOneNo` 为 `null`** | 测试当关键参数 `hbOneNo` 为 `null` 时，接口能返回参数错误。 | 无 | `hbOneNo`: `null` | | 1. `Response.code` 为参数错误码 (如 400)。<br>2. `Response.description` 包含参数不能为空的提示。 |
| **TC-E-002** | **`hbOneNo` 为空字符串** | 测试当关键参数 `hbOneNo` 为空字符串时，接口能返回参数错误。 | 无 | `hbOneNo`: `""` | | 1. `Response.code` 为参数错误码 (如 400)。<br>2. `Response.description` 包含参数不能为空的提示。 |
| **TC-E-003** | **`hbOneNo` 包含特殊字符** | 测试输入一个疑似SQL注入的字符串，验证接口的安全性。 | 无 | `hbOneNo`: `' OR '1'='1` | | 1. `Response.code` 为 `200`。<br>2. `Response.data.getRelationList()` 返回空列表，确认无SQL注入风险。 |
| **TC-E-004** | **依赖服务 `wechatCustRelationService` 抛出异常** | 通过Mock模拟依赖服务抛出未知异常，验证接口的容错能力和全局异常处理是否生效。 | Mock `wechatCustRelationService.queryWechatUserRelation` 方法，使其抛出 `RuntimeException`。 | `hbOneNo`: `9200000103` | | 由于接口实现中没有 `try-catch` 块，预期由Dubbo或全局异常处理器捕获，并返回系统错误响应。例如 `Response.code` 为系统错误码。 |

## 6. 测试数据准备

为了支持上述测试用例，建议在数据库中准备以下数据：

- **客户1 (单一关联):**
  - `cm_wechat_cust_info`: `HBONE_NO`='9200000103', `EXTERNAL_USER_ID`='cust_1'
  - `cm_wechat_cust_relation`: `EXTERNAL_USER_ID`='cust_1', `conscode`='emp_A', `status`=1
  - `cm_wechat_emp`: `conscode`='emp_A', `emp_name`='理财师A'

- **客户2 (多个关联):**
  - `cm_wechat_cust_info`: `HBONE_NO`='9200001009', `EXTERNAL_USER_ID`='cust_2'
  - `cm_wechat_cust_relation`: `EXTERNAL_USER_ID`='cust_2', `conscode`='emp_A', `status`=1
  - `cm_wechat_cust_relation`: `EXTERNAL_USER_ID`='cust_2', `conscode`='emp_B', `status`=1
  - `cm_wechat_emp`: `conscode`='emp_A', `emp_name`='理财师A'
  - `cm_wechat_emp`: `conscode`='emp_B', `emp_name`='理财师B'

- **客户3 (无关联):**
  - `cm_wechat_cust_info`: `HBONE_NO`='9200001010', `EXTERNAL_USER_ID`='cust_3'

- **客户4 (无效关联):**
  - `cm_wechat_cust_info`: `HBONE_NO`='9200001011', `EXTERNAL_USER_ID`='cust_4'
  - `cm_wechat_cust_relation`: `EXTERNAL_USER_ID`='cust_4', `conscode`='emp_C', `status`=2
  - `cm_wechat_emp`: `conscode`='emp_C', `emp_name`='理财师C'
