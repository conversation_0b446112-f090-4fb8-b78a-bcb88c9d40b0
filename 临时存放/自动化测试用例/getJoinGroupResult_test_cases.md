
# `getJoinGroupResult` 接口测试用例

> **作者:** hongdong.xie
> **日期:** 2025-07-31 11:23:48

## 1. 接口概述

`getJoinGroupResult` 接口用于查询指定客户在指定群聊中的状态，包括是否入群、入群时间、退群时间等信息。

**接口地址:** `com.howbuy.crm.wechat.client.producer.cmwechatgroup.CmWechatGroupQueryService#getJoinGroupResult`

**输入参数:**

| 参数名  | 类型   | 是否必填 | 描述     |
| :------ | :----- | :------- | :------- |
| hbOneNo | String | 是       | 一账通号 |
| groupId | String | 是       | 群聊ID   |

**输出结果:** `Response<JoinGroupResultVO>`

## 2. 依赖服务和数据

### 2.1. 依赖服务

- `com.howbuy.crm.wechat.service.service.wechatgroup.WechatGroupUserService`

### 2.2. 依赖数据表

- **`cm_wechat_cust_info`**: 客户信息表，用于根据 `hbOneNo` 查询客户的 `external_user_id`。
- **`cm_wechat_group_user_new`**: 客户与群聊关系表，用于查询客户在群聊中的状态、入群时间、退群时间等。
- **`cm_wechat_group`**: 群聊信息表，用于在客户已退群但关系表中没有退群时间时，获取群的解散状态和更新时间。

## 3. 测试场景

### 3.1. 正常场景

| 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期 `joinGroupStatus` | 预期其他返回 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **客户已入群** | `hbOneNo` 在 `cm_wechat_cust_info` 中存在，且 `external_user_id` 在 `cm_wechat_group_user_new` 中与 `groupId` 关联，且 `userchatflag` 为 `0`。 | `hbOneNo` 和 `groupId` | | `ALREADY_JOIN` | `joinGroupTime` 为 `cm_wechat_group_user_new.join_time` 的格式化时间。 |
| **客户已退群** (关系表中有退群时间) | `hbOneNo` 在 `cm_wechat_cust_info` 中存在，且 `external_user_id` 在 `cm_wechat_group_user_new` 中与 `groupId` 关联，且 `userchatflag` 不为 `0`，且 `leave_time` 不为空。 | `hbOneNo` 和 `groupId` | | `LEAVE_JOIN` | `joinGroupTime` 为 `cm_wechat_group_user_new.join_time` 的格式化时间。<br>`existGroupTime` 为 `cm_wechat_group_user_new.leave_time` 的格式化时间。 |
| **客户已退群** (群已解散，关系表中无退群时间) | `hbOneNo` 在 `cm_wechat_cust_info` 中存在，且 `external_user_id` 在 `cm_wechat_group_user_new` 中与 `groupId` 关联，且 `userchatflag` 不为 `0`，且 `leave_time` 为空。同时，`cm_wechat_group` 表中对应 `groupId` 的 `chat_flag` 为 `1` (已解散)。 | `hbOneNo` 和 `groupId` | | `LEAVE_JOIN` | `joinGroupTime` 为 `cm_wechat_group_user_new.join_time` 的格式化时间。<br>`existGroupTime` 为 `cm_wechat_group.update_time` 的格式化时间。 |
| **客户未入群** (在 `cm_wechat_group_user_new` 中无记录) | `hbOneNo` 在 `cm_wechat_cust_info` 中存在，但 `external_user_id` 在 `cm_wechat_group_user_new` 中没有与 `groupId` 的关联记录。 | `hbOneNo` 和 `groupId` | | `UN_JOIN` | `joinGroupTime` 和 `existGroupTime` 均为 `null`。 |
| **客户不存在** (在 `cm_wechat_cust_info` 中无记录) | `hbOneNo` 在 `cm_wechat_cust_info` 中不存在。 | `hbOneNo` 和 `groupId` | | `UN_JOIN` | `joinGroupTime` 和 `existGroupTime` 均为 `null`。 |

### 3.2. 异常场景

| 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期 `code` | 预期 `description` |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **`hbOneNo` 为空或 `null`** | 无 | `hbOneNo` 为 `null` 或 `""` | | `PARAM_ERROR` | "参数错误" |
| **`groupId` 为空或 `null`** | 无 | `groupId` 为 `null` 或 `""` | | `PARAM_ERROR` | "参数错误" |
| **`hbOneNo` 和 `groupId` 均为空或 `null`** | 无 | `hbOneNo` 和 `groupId` 均为 `null` 或 `""` | | `PARAM_ERROR` | "参数错误" |

## 4. 测试数据准备

为了覆盖以上测试场景，需要准备以下数据：

- **`cm_wechat_cust_info` 表:**
  - 包含 `hbOneNo` 存在和不存在的记录。
- **`cm_wechat_group_user_new` 表:**
  - 客户已入群的记录 (`userchatflag = 0`)。
  - 客户已退群的记录 (`userchatflag != 0`, `leave_time` 有值)。
  - 客户已退群的记录 (`userchatflag != 0`, `leave_time` 为 `null`)。
- **`cm_wechat_group` 表:**
  - 群已解散的记录 (`chat_flag = 1`)。
  - 群未解散的记录 (`chat_flag = 0`)。

---
