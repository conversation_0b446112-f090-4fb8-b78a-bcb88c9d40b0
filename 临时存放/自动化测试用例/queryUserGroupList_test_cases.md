# `queryUserGroupList` 接口测试用例

## 1. 接口概述

- **接口名称:** `queryUserGroupList`
- **接口路径:** `com.howbuy.crm.wechat.client.producer.cmwechatgroup.CmWechatGroupQueryService.queryUserGroupList(QueryUserGroupRequest)`
- **功能描述:** 根据指定条件（客户HbOneNo、客户企微昵称、理财师部门ID列表）分页查询客户所在的企微群聊列表。

## 2. 依赖数据表范围

为了给接口准备真实有效的测试数据，需要确保以下 **5个表** 中的数据是相互关联且逻辑正确的。

| 表名 | 用途 | 关键字段 | 关联逻辑 |
| :--- | :--- | :--- | :--- |
| `cm_wechat_cust_info` | 存储企微客户的基本信息。 | `EXTERNAL_USER_ID`, `HBONE_NO`, `NICK_NAME` | 查询的主要入口，根据 `HBONE_NO` 和 `NICK_NAME` 筛选客户。 |
| `cm_wechat_cust_relation` | 存储企微客户和理财师的关联关系。 | `EXTERNAL_USER_ID`, `conscode` | 连接客户和理财师。 |
| `cm_wechat_emp` | 存储企微员工（理财师）的信息。 | `emp_id`, `dept_id` | 根据 `dept_id` 筛选理财师。 |
| `cm_wechat_group_user` | 存储企微客户和企微群的关联关系。 | `EXTERNAL_USER_ID`, `CHAT_ID`, `userchatflag` | 根据客户 `EXTERNAL_USER_ID` 找出其所在的群。 |
| `cm_wechat_group` | 存储企微群本身的信息。 | `CHAT_ID`, `CHAT_FLAG` | 判断群状态（如是否解散），确保群的有效性。 |

**数据准备核心思路:**
一个有效的测试数据至少需要包含：一个客户 (`cm_wechat_cust_info`)，他/她关联了一位特定部门的理财师 (`cm_wechat_cust_relation` -> `cm_wechat_emp`)，并且这位客户加入了一个或多个有效的群聊 (`cm_wechat_group_user` -> `cm_wechat_group`)。

## 3. 输入参数 (`QueryUserGroupRequest`)

| 参数名 | 类型 | 是否必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `deptIdList` | `List<Integer>` | 是 | 理财师部门ID列表，不能为空。 |
| `hbOneNo` | `String` | 否 | 客户唯一标识 (HbOneNo)。 |
| `wechatNickName` | `String` | 否 | 客户的企微昵称，支持模糊查询。 |
| `pageNo` | `Integer` | 是 | 页码，默认为1。 |
| `pageSize` | `Integer` | 是 | 每页数量，默认为10。 |

## 4. 输出结果 (`Response<UserGroupVO>`)

- **成功:** 返回 `Response.ok(UserGroupVO)`，其中 `UserGroupVO` 包含群聊列表和分页信息。
- **失败:** 返回带有错误码和错误信息的 `Response` 对象。

## 5. 测试用例

### 4.1. 正常场景测试

| 用例ID | 用例标题 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-N-001** | **全参数查询** | 数据库中存在完全匹配 `hbOneNo`, `wechatNickName`, `deptIdList` 的数据。 | `deptIdList`: `[650]`<br>`hbOneNo`: `9200000103`<br>`wechatNickName`: `赵木`<br>`pageNo`: `1`<br>`pageSize`: `10` | {"deptIdList": [650], "hbOneNo": "9200000103", "wechatNickName": "赵木", "pageNo": 1, "pageSize": 10} | 1. `Response.code` 为 `200`。<br>2. `Response.data` 不为 `null`。<br>3. 返回的数据列表符合所有查询条件，且分页正确。 |
| **TC-N-002** | **仅使用必填参数查询** | 数据库中存在 `deptIdList` 对应的数据。 | `deptIdList`: `[650, 607]`<br>`hbOneNo`: `null`<br>`wechatNickName`: `null`<br>`pageNo`: `1`<br>`pageSize`: `10` | {"deptIdList": [650, 607], "hbOneNo": null, "wechatNickName": null, "pageNo": 1, "pageSize": 10} | 1. `Response.code` 为 `200`。<br>2. `Response.data` 不为 `null`。<br>3. 返回的数据列表符合 `deptIdList` 查询条件，且分页正确。 |
| **TC-N-003** | **使用 `hbOneNo` 查询** | 数据库中存在 `hbOneNo` 和 `deptIdList` 对应的数据。 | `deptIdList`: `[650]`<br>`hbOneNo`: `9200001009`<br>`wechatNickName`: `null`<br>`pageNo`: `1`<br>`pageSize`: `10` | {"deptIdList": [650], "hbOneNo": "9200001009", "wechatNickName": null, "pageNo": 1, "pageSize": 10} | 1. `Response.code` 为 `200`。<br>2. `Response.data` 不为 `null`。<br>3. 返回的数据列表符合 `hbOneNo` 和 `deptIdList` 查询条件。 |
| **TC-N-004** | **使用 `wechatNickName` 模糊查询** | 数据库中存在客户昵称包含 "生" 且 `deptIdList` 对应的数据。 | `deptIdList`: `[607]`<br>`hbOneNo`: `null`<br>`wechatNickName`: `生`<br>`pageNo`: `1`<br>`pageSize`: `10` | {"deptIdList": [607], "hbOneNo": null, "wechatNickName": "生", "pageNo": 1, "pageSize": 10} | 1. `Response.code` 为 `200`。<br>2. `Response.data` 不为 `null`。<br>3. 返回的数据列表中的客户昵称均包含 "生"。 |
| **TC-N-005** | **分页测试-查询第二页** | 数据库中存在超过一页的数据。 | `deptIdList`: `[650]`<br>`hbOneNo`: `null`<br>`wechatNickName`: `null`<br>`pageNo`: `2`<br>`pageSize`: `5` | {"deptIdList": [650], "hbOneNo": null, "wechatNickName": null, "pageNo": 2, "pageSize": 5} | 1. `Response.code` 为 `200`。<br>2. `Response.data` 不为 `null`。<br>3. 返回的数据为第二页的5条记录。 |
| **TC-N-006** | **查询结果为空** | 数据库中不存在任何匹配查询条件的数据。 | `deptIdList`: `[999]`<br>`hbOneNo`: `C_NON_EXIST`<br>`wechatNickName`: `不存在的用户`<br>`pageNo`: `1`<br>`pageSize`: `10` | {"deptIdList": [999], "hbOneNo": "C_NON_EXIST", "wechatNickName": "不存在的用户", "pageNo": 1, "pageSize": 10} | 1. `Response.code` 为 `200`。<br>2. `Response.data` 不为 `null`，但其内部的群组列表为空。<br>3. `Response.data` 中的分页信息 `total` 为 `0`。 |

### 4.2. 异常及边界场景测试

| 用例ID | 用例标题 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-E-001** | **`deptIdList` 为 `null`** | 无 | `deptIdList`: `null`<br>其他参数任意 | {"deptIdList": null, "hbOneNo": "9200000103", "wechatNickName": "赵木", "pageNo": 1, "pageSize": 10} | 1. `Response.code` 为 `ResponseCodeEnum.PARAM_ERROR` 的 code (例如 400)。<br>2. `Response.description` 为 `ResponseCodeEnum.PARAM_ERROR` 的描述。 |
| **TC-E-002** | **`deptIdList` 为空集合** | 无 | `deptIdList`: `[]` (empty list)<br>其他参数任意 | {"deptIdList": [], "hbOneNo": "9200000103", "wechatNickName": "赵木", "pageNo": 1, "pageSize": 10} | 1. `Response.code` 为 `ResponseCodeEnum.PARAM_ERROR` 的 code (例如 400)。<br>2. `Response.description` 为 `ResponseCodeEnum.PARAM_ERROR` 的描述。 |
| **TC-B-003** | **`pageNo` 为 `null`** | 无 | `deptIdList`: `[650]`<br>`pageNo`: `null`<br>其他参数任意 | {"deptIdList": [650], "hbOneNo": "9200000103", "wechatNickName": "赵木", "pageNo": null, "pageSize": 10} | 接口实现中未对 `pageNo` 和 `pageSize` 做 `null` 值校验，预期底层服务会使用默认值（如 `pageNo=1`）进行查询，并返回 `code=200` 的成功结果。 |
| **TC-B-004** | **`pageSize` 为 `null`** | 无 | `deptIdList`: `[650]`<br>`pageSize`: `null`<br>其他参数任意 | {"deptIdList": [650], "hbOneNo": "9200000103", "wechatNickName": "赵木", "pageNo": 1, "pageSize": null} | 接口实现中未对 `pageNo` 和 `pageSize` 做 `null` 值校验，预期底层服务会使用默认值（如 `pageSize=10`）进行查询，并返回 `code=200` 的成功结果。 |
| **TC-B-005** | **`pageNo` 或 `pageSize` 为0或负数** | 无 | `deptIdList`: `[650]`<br>`pageNo`: `0`<br>`pageSize`: `-1` | {"deptIdList": [650], "hbOneNo": "9200000103", "wechatNickName": "赵木", "pageNo": 0, "pageSize": -1} | 接口实现中未做校验，预期底层分页插件或SQL会处理此种情况，可能返回第一页数据或空列表。建议底层服务进行校验和优化。 |
| **TC-E-006** | **依赖服务 `wechatGroupUserService.queryUserGroupList` 抛出异常** | Mock `wechatGroupUserService.queryUserGroupList` 方法，使其抛出 `RuntimeException`。 | `deptIdList`: `[650]`<br>其他参数任意 | {"deptIdList": [650], "hbOneNo": "9200000103", "wechatNickName": "赵木", "pageNo": 1, "pageSize": 10} | 由于接口实现中没有 `try-catch` 块，预期由Dubbo或全局异常处理器捕获，并返回系统错误响应。例如 `Response.code` 为 `ResponseCodeEnum.SYS_ERROR` 的 code。 |

## 6. 接口代码逻辑脑图

```plantuml
@startmindmap
* 查询客户群聊列表 (queryUserGroupList)
** 输入参数校验 (QueryUserGroupRequest)
*** 理财师部门ID列表 (deptIdList)
**** 正例: 非空列表
***** 调用 `wechatGroupUserService.queryUserGroupList`
***** 返回 `Response.ok(result)`
**** 反例: null 或空列表
***** 返回 `PARAM_ERROR` (400)
*** 客户唯一标识 (hbOneNo)
**** (代码中未做显式校验，由 `wechatGroupUserService` 处理)
*** 客户企微昵称 (wechatNickName)
**** (代码中未做显式校验，由 `wechatGroupUserService` 处理)
*** 页码 (pageNo)
**** (代码中未做显式校验，由 `wechatGroupUserService` 处理)
*** 每页数量 (pageSize)
**** (代码中未做显式校验，由 `wechatGroupUserService` 处理)
** 核心业务逻辑
*** 调用 `wechatGroupUserService.queryUserGroupList`
**** 成功: 返回 `UserGroupVO`
***** 封装并返回 `Response.ok(result)`
**** 异常: 抛出异常
***** (当前代码未捕获，由Dubbo或全局异常处理器处理)
***** 预期返回 `SYS_ERROR`
** 输出结果
*** 成功
**** `Response.code` = 200
**** `Response.data` = `UserGroupVO`
*** 失败 (参数错误)
**** `Response.code` = 400
**** `Response.description` = "参数错误"
@endmindmap
```