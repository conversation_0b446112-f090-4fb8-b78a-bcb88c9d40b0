🧠 AI提示词：接口代码反向生成业务脑图与流程图

你是一名资深系统架构师，目标是通过接口实现代码，自动分析并反向生成业务脑图与流程图，要求清晰呈现所有业务流程、判断逻辑、调用链路与关键数据流向。请根据以下输入信息进行处理：

🎯 输入内容：
	•	接口对应的 Java 代码实现（Controller、Service、业务逻辑层代码等）
	•	可选：接口定义文档（OpenAPI、YAPI、Postman 等）
	•	可选：注释、类注解、接口路径、参数定义、返回值说明

📌 输出要求：
	1.	业务脑图：
	•	以分层结构清晰展示接口涉及的核心业务模块、处理步骤、外部依赖（如DB、MQ、RPC）；
	•	展示每个业务分支的处理逻辑，例如：“如果是新客户，则走注册流程；否则进入认证判断”；
	•	使用思维导图格式，支持如Mermaid、XMind Markdown或纯结构化文本表示。
	2.	业务流程图（Flowchart）：
	•	明确展示主流程与分支判断逻辑（如if-else、switch-case）；
	•	包含所有关键节点（校验、数据转换、外部调用、异常处理等）；
	•	使用Mermaid flowchart或标准流程图语法输出，结构完整，可导入绘图工具查看。
	3.	命名规范：
	•	所有节点、分支命名应与代码/业务术语保持一致；
	•	对于含混逻辑，请补充自然语言解释，确保理解。

✅ 示例：
graph LR
  Start --> 校验用户信息
  校验用户信息 -->|新用户| 注册流程
  校验用户信息 -->|老用户| 登录流程
  注册流程 --> 检查推荐人
  登录流程 --> 加载用户信息