package com.howbuy.crm.wechat.dao.mapper;

import com.howbuy.crm.wechat.dao.bo.UserGroupInfoBO;
import com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: crm-wechat 
 * @author: yu.zhang
 * @date: 2023/6/28 10:03 
 * @since JDK 1.8
 * @version: 1.0
 */
@Mapper
public interface CmWechatCustInfoMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(CmWechatCustInfoPO record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(CmWechatCustInfoPO record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    CmWechatCustInfoPO selectByPrimaryKey(Long id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(CmWechatCustInfoPO record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(CmWechatCustInfoPO record);


    /**
     * @description 查询指定部门下的外部客户列表，如果客户没有加群，则只返回客户信息
     * @param externalUserIds
     * @return
     * <AUTHOR>
     * @date 2024/5/27 3:58 PM
     * @since JDK 1.8
     */
    List<UserGroupInfoBO> selectUserGroupList(@Param("externalUserIds") List<String> externalUserIds);

    /**
     * @description
     * @param hbOneNo
     * @param wechatNickName
     * @param deptIdList
     * @return
     * <AUTHOR>
     * @date 2024/6/17 5:19 PM
     * @since JDK 1.8
     */
    List<String> selectExternalUserIds(@Param("hbOneNo") String hbOneNo,
                                       @Param("wechatNickName") String wechatNickName,
                                       @Param("deptIdList")List<Integer> deptIdList);

    /**
     * @description  查询指定部门下的外部客户总数
     * @param hbOneNo    一账通号
     * @param wechatNickName   微信昵称
     * @param deptIdList 部门 ID 列表
     * @return
     * <AUTHOR>
     * @date 2024/5/27 4:05 PM
     * @since JDK 1.8
     */
    int countUserGroupList(@Param("hbOneNo") String hbOneNo, @Param("wechatNickName") String wechatNickName, @Param("deptIdList")List<Integer> deptIdList);

    /**
     * @description 根据一账通号获取客户企业微信ID
     * @param hbOneNo
     * @return
     * <AUTHOR>
     * @date 2024/5/27 4:17 PM
     * @since JDK 1.8
     */
    String getExternalUserIDByHboneNo(@Param("hbOneNo")String hbOneNo);

    /**
     * @description 更新一帐通(账户中心mq接受时使用)
     * @param unionId
     * @param hbOneNo
     * @return
     * <AUTHOR>
     * @date 2024/6/19 3:19 PM
     * @since JDK 1.8
     */
    int updateHbOneNoByUnionId(@Param("unionId")String unionId, @Param("hbOneNo")String hbOneNo);

}