package com.howbuy.crm.wechat.dao.po;

import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description: crm-wechat 
 * @author: yu.zhang 
 * @date: 2023/6/28 10:03 
 * @since JDK 1.8
 * @version: 1.0 
 */

/**
 * 客户微信信息表
 */
@Getter
@Setter
@ToString
public class CmWechatCustInfoPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 一账通号
     */
    private String hboneNo;

    /**
     * 微信UnionId
     */
    private String unionid;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 企业编码1好买财富2好买基金
     */
    private String companyNo;

    /**
     * 外部应用用户ID
     */
    private String externalUserId;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 微信头像
     */
    private String wechatAvatar;
}