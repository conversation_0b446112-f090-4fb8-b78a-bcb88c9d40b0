package com.howbuy.crm.wechat.dao.mapper.customize;

import com.howbuy.crm.wechat.dao.po.CmWechatDeptPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: 企业微信部门表
 * @author: yu.zhang
 * @date: 2023/6/12 13:07 
 * @since JDK 1.8
 * @version: 1.0
 */
@Mapper
public interface CustomizeCmWechatDeptMapper {

    /**
     * @description:根据部门ID查询部门
     * @param deptId
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatDeptPO>
     * @author: yu.zhang
     * @date: 2023/6/12 17:41
     * @since JDK 1.8
     */
    List<CmWechatDeptPO> listWechatDeptByDeptId(@Param("deptId") Integer deptId, @Param("companyNo") String companyNo);

    /**
     * @description:查询全量数据
     * @param
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatDeptPO>
     * @author: yu.zhang
     * @date: 2023/7/27 15:49
     * @since JDK 1.8
     */
    List<CmWechatDeptPO> listAllWechatDept();
}