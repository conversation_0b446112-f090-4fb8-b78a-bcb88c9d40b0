package com.howbuy.crm.wechat.dao.mapper;

import com.howbuy.crm.wechat.dao.po.CmWechatRefreshConscustPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: shuai.zhang
 * @Date: 2024/3/26
 * @Description: 
*/
public interface CmWechatRefreshConscustMapper {
    int deleteByPrimaryKey(String id);

    int insert(CmWechatRefreshConscustPO record);

    int insertSelective(CmWechatRefreshConscustPO record);

    CmWechatRefreshConscustPO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CmWechatRefreshConscustPO record);

    int updateByPrimaryKey(CmWechatRefreshConscustPO record);

    int batchInsert(@Param("list") List<CmWechatRefreshConscustPO> list);

    /**
     * @description:( 投顾客户微信刷新状态表 获取当天已处理数据)
     * @param nowDate	
     * @param status	
     * @param dealtype
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatRefreshConscustPO>
     * @author: shuai.zhang
     * @date: 2024/3/26 15:38
     * @since JDK 1.8
     */
    List<CmWechatRefreshConscustPO> getCmWechatRefreshConscustNowDateDeal(@Param("nowDate")String nowDate, @Param("dealstatus")String status,@Param("dealtype")String dealtype);

    /**
     * @description:(初始化当天所有待刷新的投顾企微账号)
     * @param nowDate	
     * @param dealType
     * @param needRefreshWechatConsCode
     * @return int
     * @author: shuai.zhang
     * @date: 2024/3/26 18:38
     * @since JDK 1.8
     */
    int insertCmWechatRefreshConscustNowDate(@Param("nowDate")String nowDate, @Param("dealType")String dealType, @Param("needRefreshWechatConsCode")List<String> needRefreshWechatConsCode);

    /**
     * @description:(根据当前日期和任务类型,更新所有状态)
     * @param nowDate
     * @param dealType
     * @param dealStatus
     * @return int
     * @author: shuai.zhang
     * @date: 2024/3/27 18:14
     * @since JDK 1.8
     */
    int updateCmWechatRefreshConscustStatusByDateAndType(@Param("nowDate")String nowDate, @Param("dealType")String dealType,@Param("dealStatus")String dealStatus);
}