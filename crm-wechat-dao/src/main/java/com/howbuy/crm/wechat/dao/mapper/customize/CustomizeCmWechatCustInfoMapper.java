package com.howbuy.crm.wechat.dao.mapper.customize;

import com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: crm-wechat 
 * @author: yu.zhang
 * @date: 2023/6/25 14:15 
 * @since JDK 1.8
 * @version: 1.0
 */
@Mapper
public interface CustomizeCmWechatCustInfoMapper {

    /**
     * @description:根据外部用户ID查询对应客户信息
     * @param externalUserId	
     * @param companyNo
     * @return com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO
     * @author: yu.zhang
     * @date: 2023/6/25 15:34
     * @since JDK 1.8
     */
    List<CmWechatCustInfoPO> listWechatCustByExternalUserId(@Param("externalUserId") String externalUserId, @Param("companyNo") String companyNo);

    /**
     * @description:(请在此添加描述)
     * @param hboneNo	
     * @param companyNo
     * @return com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO
     * @author: yu.zhang
     * @date: 2023/6/25 15:53
     * @since JDK 1.8
     */
    CmWechatCustInfoPO getExternalUserByHboneNo(@Param("hboneNo") String hboneNo, @Param("companyNo") String companyNo);

    /**
     * @description:根据外部联系人id查询微信客户信息
     * @param externalUserId
     * @param companyNo
     * @return com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO
     * <AUTHOR>
     * @date 2024/9/10 13:21
     * @since JDK 1.8
     */
    CmWechatCustInfoPO getWechatCustByExternalUserId(@Param("externalUserId") String externalUserId, @Param("companyNo") String companyNo);
}