<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.CmWechatRefreshConscustMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.CmWechatRefreshConscustPO">
    <!--@mbg.generated-->
    <!--@Table cm_wechat_refresh_conscust-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="DEAL_DATA" jdbcType="LONGVARCHAR" property="dealData" />
    <result column="DEAL_DATE" jdbcType="VARCHAR" property="dealDate" />
    <result column="DEAL_TYPE" jdbcType="VARCHAR" property="dealType" />
    <result column="DEAL_STATUS" jdbcType="VARCHAR" property="dealStatus" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, DEAL_DATA, DEAL_DATE, DEAL_TYPE, DEAL_STATUS, CREATOR, CREATE_TIME, MODIFIER, 
    UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from cm_wechat_refresh_conscust
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from cm_wechat_refresh_conscust
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatRefreshConscustPO">
    <!--@mbg.generated-->
    insert into cm_wechat_refresh_conscust (ID, DEAL_DATA, DEAL_DATE, 
      DEAL_TYPE, DEAL_STATUS, CREATOR, 
      CREATE_TIME, MODIFIER, UPDATE_TIME
      )
    values (#{id,jdbcType=VARCHAR}, #{dealData,jdbcType=LONGVARCHAR}, #{dealDate,jdbcType=VARCHAR}, 
      #{dealType,jdbcType=VARCHAR}, #{dealStatus,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatRefreshConscustPO">
    <!--@mbg.generated-->
    insert into cm_wechat_refresh_conscust
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="dealData != null">
        DEAL_DATA,
      </if>
      <if test="dealDate != null">
        DEAL_DATE,
      </if>
      <if test="dealType != null">
        DEAL_TYPE,
      </if>
      <if test="dealStatus != null">
        DEAL_STATUS,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="dealData != null">
        #{dealData,jdbcType=LONGVARCHAR},
      </if>
      <if test="dealDate != null">
        #{dealDate,jdbcType=VARCHAR},
      </if>
      <if test="dealType != null">
        #{dealType,jdbcType=VARCHAR},
      </if>
      <if test="dealStatus != null">
        #{dealStatus,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatRefreshConscustPO">
    <!--@mbg.generated-->
    update cm_wechat_refresh_conscust
    <set>
      <if test="dealData != null">
        DEAL_DATA = #{dealData,jdbcType=LONGVARCHAR},
      </if>
      <if test="dealDate != null">
        DEAL_DATE = #{dealDate,jdbcType=VARCHAR},
      </if>
      <if test="dealType != null">
        DEAL_TYPE = #{dealType,jdbcType=VARCHAR},
      </if>
      <if test="dealStatus != null">
        DEAL_STATUS = #{dealStatus,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatRefreshConscustPO">
    <!--@mbg.generated-->
    update cm_wechat_refresh_conscust
    set DEAL_DATA = #{dealData,jdbcType=LONGVARCHAR},
      DEAL_DATE = #{dealDate,jdbcType=VARCHAR},
      DEAL_TYPE = #{dealType,jdbcType=VARCHAR},
      DEAL_STATUS = #{dealStatus,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into cm_wechat_refresh_conscust
    (ID, DEAL_DATA, DEAL_DATE, DEAL_TYPE, DEAL_STATUS, CREATOR, CREATE_TIME, MODIFIER, 
      UPDATE_TIME)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.dealData,jdbcType=LONGVARCHAR}, #{item.dealDate,jdbcType=VARCHAR}, 
        #{item.dealType,jdbcType=VARCHAR}, #{item.dealStatus,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.modifier,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>

  <select id="getCmWechatRefreshConscustNowDateDeal" resultMap="BaseResultMap">
    select  <include refid="Base_Column_List" />
    from CM_WECHAT_REFRESH_CONSCUST a
    where 1=1
    <if test="nowDate != null">and a.DEAL_DATE=#{nowDate}</if>
    <if test="dealstatus != null">and a.DEAL_STATUS=#{dealstatus} </if>
    <if test="dealtype != null">and a.DEAL_TYPE=#{dealtype} </if>
  </select>

  <insert id="insertCmWechatRefreshConscustNowDate">
    insert into CM_WECHAT_REFRESH_CONSCUST (DEAL_DATA, DEAL_DATE, DEAL_STATUS, DEAL_TYPE)
    values
    <foreach collection="needRefreshWechatConsCode" item="item" separator=",">
      (#{item,jdbcType=VARCHAR}, #{nowDate,jdbcType=VARCHAR}, '0', #{dealType,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <update id="updateCmWechatRefreshConscustStatusByDateAndType">
    update CM_WECHAT_REFRESH_CONSCUST a
    set a.DEAL_STATUS = #{dealStatus,jdbcType=VARCHAR},
        a.UPDATE_TIME=now()
    where a.DEAL_DATE = #{nowDate,jdbcType=VARCHAR}
      and a.DEAL_TYPE = #{dealType,jdbcType=VARCHAR}
  </update>
</mapper>