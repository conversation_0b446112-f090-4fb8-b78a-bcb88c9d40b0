<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.customize.CustomizeCmWechatCustInfoMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO">
    <!--@mbg.generated-->
    <!--@Table `cm_wechat_cust_info`-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="HBONE_NO" jdbcType="VARCHAR" property="hboneNo" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="COMPANY_NO" jdbcType="VARCHAR" property="companyNo" />
    <result column="EXTERNAL_USER_ID" jdbcType="VARCHAR" property="externalUserId" />
    <result column="NICK_NAME" jdbcType="VARCHAR" property="nickName" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="wechat_avatar" jdbcType="VARCHAR" property="wechatAvatar"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `ID`, `HBONE_NO`, `UNIONID`, `CREATOR`, `CREATE_TIME`, `UPDATE_TIME`, `COMPANY_NO`,
    `EXTERNAL_USER_ID`, `NICK_NAME`, `MODIFIER`, `wechat_avatar`
  </sql>

  <select id="listWechatCustByExternalUserId" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from `cm_wechat_cust_info`
    where `EXTERNAL_USER_ID` = #{externalUserId,jdbcType=VARCHAR} AND `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR}
  </select>

  <select id="getExternalUserByHboneNo" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from `cm_wechat_cust_info` where `HBONE_NO` =#{hboneNo,jdbcType=VARCHAR} AND `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR}
  </select>

  <select id="getWechatCustByExternalUserId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from `cm_wechat_cust_info`
    where `EXTERNAL_USER_ID` =#{externalUserId,jdbcType=VARCHAR} AND `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR}
  </select>
</mapper>