<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.CmWechatGroupMapper">
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        `ID`,
        `CHAT_ID`,
        `STATUS`,
        `COMPANY_NO`,
        `CREATE_TIME`,
        `UPDATE_TIME`,
        `CHAT_FLAG`,
        `CHAT_NAME`,
        `CHAT_OWNER`,
        `DEPT_ID`,
        `DEPT_NAME`
    </sql>

    <select id="getByChatId" resultType="com.howbuy.crm.wechat.dao.po.CmWechatGroupPO">
        select * from cm_wechat_group where chat_id = #{chatId}
    </select>

    <insert id="save" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatGroupPO">
        insert into cm_wechat_group (`CHAT_ID`,`STATUS`,`COMPANY_NO`,`CREATE_TIME`, `CHAT_FLAG`)
        values (#{chatId}, #{status}, #{companyNo}, #{createTime}, '0')
    </insert>

    <update id="updateByChatId" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatGroupPO">
        update cm_wechat_group
        <set>
            <if test="chatName != null and chatName != ''">
                chat_name = #{chatName},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="companyNo != null and companyNo != ''">
                company_no = #{companyNo},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="chatFlag != null and chatFlag != ''">
                chat_flag = #{chatFlag},
            </if>
            <if test="chatOwner != null and chatOwner != ''">
                chat_owner = #{chatOwner},
            </if>
            <if test="deptId != null and deptId != ''">
                dept_id = #{deptId},
            </if>
            <if test="deptName != null and deptName != ''">
                dept_name = #{deptName},
            </if>
        </set>
        where chat_id = #{chatId}
    </update>

    <select id="listByCompanyNoList" resultType="com.howbuy.crm.wechat.dao.po.CmWechatGroupPO">
        select chat_id, COMPANY_NO from cm_wechat_group where CHAT_FLAG = '0'
        <if test="companyNoList != null and companyNoList.size() > 0">
            and company_no in
            <foreach collection="companyNoList" item="companyNo" open="(" separator="," close=")">
                #{companyNo}
            </foreach>
        </if>
    </select>

    <update id="deleteByChatId">
        update cm_wechat_group set chat_flag = '1',UPDATE_TIME = #{date,jdbcType=TIMESTAMP} where chat_id = #{chatId}
    </update>

    <update id="batchDeleteByChatId">
        update cm_wechat_group set chat_flag = '1' where chat_id in
        <foreach collection="list" item="chatId" open="(" separator="," close=")">
            #{chatId}
        </foreach>
    </update>

    <insert id="batchInsert">
        insert into cm_wechat_group (`CHAT_ID`,`STATUS`,`COMPANY_NO`,`CREATE_TIME`, `CHAT_FLAG`)
        values
        <foreach collection="list" item="wechatGroupListPo" separator="," >
            (#{wechatGroupListPo.chatId}, #{wechatGroupListPo.status}, #{wechatGroupListPo.companyNo}, #{wechatGroupListPo.createTime}, '0')
        </foreach>
    </insert>

    <select id="listChatIdByCompanyNoAndUserIdList" resultType="java.lang.String">
        select chat_id from cm_wechat_group where CHAT_FLAG = '0'
        <if test="companyNo != null and companyNo != ''">
            and company_no = #{companyNo}
        </if>
        <if test="userIdList != null and userIdList.size() > 0">
            and chat_owner in
            <foreach collection="userIdList" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
    </select>

    <select id="listByChatIdList" resultType="com.howbuy.crm.wechat.dao.po.CmWechatGroupPO">
        select CHAT_ID, COMPANY_NO from cm_wechat_group where CHAT_FLAG = '0'
        <if test="chatIdList != null and chatIdList.size() > 0">
            and chat_id in
            <foreach collection="chatIdList" item="chatId" open="(" separator="," close=")">
                #{chatId}
            </foreach>
        </if>
    </select>

    <select id="selectContainUserChat" resultType="com.howbuy.crm.wechat.dao.po.CmWechatGroupPO">
        select a.CHAT_ID     as chatId,
               a.COMPANY_NO  as companyNo,
               a.CHAT_NAME   as chatName,
               a.CHAT_OWNER  as chatOwner,
               a.CREATE_TIME as createTime
        from cm_wechat_group a
                 join cm_wechat_group_user b on a.CHAT_ID = b.CHAT_ID
        where b.EXTERNAL_USER_ID = #{userId,jdbcType=VARCHAR}
          and b.USERCHATFLAG = '0'
          and a.CHAT_FLAG = '0'
    </select>
</mapper>