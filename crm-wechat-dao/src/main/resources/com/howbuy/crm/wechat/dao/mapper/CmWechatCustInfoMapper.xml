<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.CmWechatCustInfoMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO">
    <!--@mbg.generated-->
    <!--@Table `cm_wechat_cust_info`-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="HBONE_NO" jdbcType="VARCHAR" property="hboneNo" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="COMPANY_NO" jdbcType="VARCHAR" property="companyNo" />
    <result column="EXTERNAL_USER_ID" jdbcType="VARCHAR" property="externalUserId" />
    <result column="NICK_NAME" jdbcType="VARCHAR" property="nickName" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="wechat_avatar" jdbcType="VARCHAR" property="wechatAvatar"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `ID`, `HBONE_NO`, `UNIONID`, `CREATOR`, `CREATE_TIME`, `UPDATE_TIME`, `COMPANY_NO`, 
    `EXTERNAL_USER_ID`, `NICK_NAME`, `MODIFIER`, `wechat_avatar`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from `cm_wechat_cust_info`
    where `ID` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from `cm_wechat_cust_info`
    where `ID` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO">
    <!--@mbg.generated-->
    insert into `cm_wechat_cust_info` (`ID`, `HBONE_NO`, `UNIONID`, 
      `CREATOR`, `CREATE_TIME`, `UPDATE_TIME`, 
      `COMPANY_NO`, `EXTERNAL_USER_ID`, `NICK_NAME`, 
      `MODIFIER`, `wechat_avatar`)
    values (#{id,jdbcType=BIGINT}, #{hboneNo,jdbcType=VARCHAR}, #{unionid,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{companyNo,jdbcType=VARCHAR}, #{externalUserId,jdbcType=VARCHAR}, #{nickName,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{wechatAvatar,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO">
    <!--@mbg.generated-->
    insert into `cm_wechat_cust_info`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `ID`,
      </if>
      <if test="hboneNo != null">
        `HBONE_NO`,
      </if>
      <if test="unionid != null">
        `UNIONID`,
      </if>
      <if test="creator != null">
        `CREATOR`,
      </if>
      <if test="createTime != null">
        `CREATE_TIME`,
      </if>
      <if test="updateTime != null">
        `UPDATE_TIME`,
      </if>
      <if test="companyNo != null">
        `COMPANY_NO`,
      </if>
      <if test="externalUserId != null">
        `EXTERNAL_USER_ID`,
      </if>
      <if test="nickName != null">
        `NICK_NAME`,
      </if>
      <if test="modifier != null">
        `MODIFIER`,
      </if>
      <if test="wechatAvatar != null">
        `wechat_avatar`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="hboneNo != null">
        #{hboneNo,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyNo != null">
        #{companyNo,jdbcType=VARCHAR},
      </if>
      <if test="externalUserId != null">
        #{externalUserId,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null">
        #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="wechatAvatar != null">
        #{wechatAvatar,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO">
    <!--@mbg.generated-->
    update `cm_wechat_cust_info`
    <set>
      <if test="hboneNo != null">
        `HBONE_NO` = #{hboneNo,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        `UNIONID` = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        `CREATOR` = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        `CREATE_TIME` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `UPDATE_TIME` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyNo != null">
        `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR},
      </if>
      <if test="externalUserId != null">
        `EXTERNAL_USER_ID` = #{externalUserId,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null">
        `NICK_NAME` = #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        `MODIFIER` = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="wechatAvatar != null">
        `wechat_avatar` = #{wechatAvatar,jdbcType=VARCHAR},
      </if>
    </set>
    where `ID` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO">
    <!--@mbg.generated-->
    update `cm_wechat_cust_info`
    set `HBONE_NO` = #{hboneNo,jdbcType=VARCHAR},
      `UNIONID` = #{unionid,jdbcType=VARCHAR},
      `CREATOR` = #{creator,jdbcType=VARCHAR},
      `CREATE_TIME` = #{createTime,jdbcType=TIMESTAMP},
      `UPDATE_TIME` = #{updateTime,jdbcType=TIMESTAMP},
      `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR},
      `EXTERNAL_USER_ID` = #{externalUserId,jdbcType=VARCHAR},
      `NICK_NAME` = #{nickName,jdbcType=VARCHAR},
      `MODIFIER` = #{modifier,jdbcType=VARCHAR},
      `wechat_avatar` = #{wechatAvatar,jdbcType=VARCHAR}
    where `ID` = #{id,jdbcType=BIGINT}
  </update>


  <resultMap id="userGroupMap" type="com.howbuy.crm.wechat.dao.bo.UserGroupInfoBO">
    <id column="hbone_no" property="hboneNo" jdbcType="VARCHAR"/>
    <id column="NICK_NAME" property="nickName" jdbcType="VARCHAR"/>
    <id column="CHAT_ID" property="chatId" jdbcType="VARCHAR"/>
    <id column="userchatflag" property="userChatFlag" jdbcType="VARCHAR"/>
    <id column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
    <id column="EXTERNAL_USER_ID" property="externalUserId" jdbcType="VARCHAR"/>
  </resultMap>

  <select id="selectUserGroupList" resultMap="userGroupMap">
    select e.hbone_no, e.NICK_NAME, w.CHAT_ID, w.userchatflag, r.add_time, e.EXTERNAL_USER_ID
    from cm_wechat_cust_info e
           left join cm_wechat_cust_relation r on e.EXTERNAL_USER_ID = r.EXTERNAL_USER_ID
           left join cm_wechat_group_user w on w.EXTERNAL_USER_ID = e.EXTERNAL_USER_ID
    where e.EXTERNAL_USER_ID in
    <choose>
      <when test="externalUserIds != null and externalUserIds.size() != 0">
        <foreach collection="externalUserIds" item="externalUserId" open="(" separator="," close=")">
          #{externalUserId}
        </foreach>
      </when>
      <otherwise>
          ('')
      </otherwise>
    </choose>
  </select>


  <select id="countUserGroupList" resultType="int">
    select count(1) cc from (
    select distinct a.EXTERNAL_USER_ID
    from cm_wechat_cust_info a,
         cm_wechat_cust_relation k,
         cm_wechat_emp l
    where l.del_flag = '0' and l.company_no = '1'
    and a.EXTERNAL_USER_ID = k.EXTERNAL_USER_ID
      and k.conscode = l.emp_id
      and k.add_time is not null
    <if test="deptIdList != null and deptIdList.size() != 0">
      and l.dept_id in
      <foreach collection="deptIdList" item="deptId" index="index" open="(" separator="," close=")">
        #{deptId}
      </foreach>
    </if>
    <if test="hbOneNo != null and hbOneNo != ''">
      and a.hbone_no = #{hbOneNo,jdbcType=VARCHAR}
    </if>
    <if test="wechatNickName != null and wechatNickName != ''">
      <bind name="pattern" value="'%' + wechatNickName + '%'"/>
      and a.NICK_NAME like #{pattern,jdbcType=VARCHAR}
    </if>
    ) as akEUI
  </select>

  <select id="getExternalUserIDByHboneNo" resultType="java.lang.String">
    select a.EXTERNAL_USER_ID from cm_wechat_cust_info a where a.hbone_no=#{hbOneNo}
  </select>

  <select id="selectExternalUserIds" resultType="java.lang.String">
    select distinct a.EXTERNAL_USER_ID as externaluserid
    from cm_wechat_cust_info a,
    cm_wechat_cust_relation k,
    cm_wechat_emp l
    where a.EXTERNAL_USER_ID = k.EXTERNAL_USER_ID
    and l.del_flag = '0' and l.company_no = '1'
    and k.conscode = l.emp_id
    and k.add_time is not null
    <if test="deptIdList != null and deptIdList.size() != 0">
      and l.dept_id in
      <foreach collection="deptIdList" item="deptId" index="index" open="(" separator="," close=")">
        #{deptId}
      </foreach>
    </if>
    <if test="hbOneNo != null and hbOneNo != ''">
      and a.hbone_no = #{hbOneNo,jdbcType=VARCHAR}
    </if>
    <if test="wechatNickName != null and wechatNickName != ''">
      <bind name="pattern" value="'%' + wechatNickName + '%'"/>
      and a.NICK_NAME like #{pattern,jdbcType=VARCHAR}
    </if>

  </select>

  <update id="updateHbOneNoByUnionId">
    UPDATE CM_WECHAT_CUST_INFO T
    SET T.HBONE_NO = #{hbOneNo, jdbcType=VARCHAR},
    T.MODIFIER = 'mq-sys',
    T.UPDATE_TIME = now()
    WHERE T.UNIONID = #{unionId, jdbcType=VARCHAR}
  </update>
</mapper>