<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.howbuy.crm</groupId>
    <artifactId>crm-wechat</artifactId>
    <packaging>pom</packaging>
    <version>2.0.4.2-RELEASE</version>

    <properties>
        <java.version>1.8</java.version>
        <dubbo.version>3.2.12</dubbo.version>
<!--        <dubbo.version>2.7.15</dubbo.version>-->
        <druid.version>1.2.8</druid.version>
        <log4j.version>2.15.0</log4j.version>
        <mybatis.version>2.2.2</mybatis.version>
        <zkclient.version>0.4</zkclient.version>
        <fastjson.version>2.0.32</fastjson.version>
        <disruptor.version>3.4.2</disruptor.version>
        <zookeeper.version>3.4.13</zookeeper.version>
        <pagehelper.version>5.3.0</pagehelper.version>
        <commons.codec>1.15</commons.codec>
        <!--这个maven 编译编译版本一定不能删 要不然 会默认使用jdk1.5编译-->
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <spring-cloud.version>Hoxton.SR9</spring-cloud.version>
        <spring-boot.version>2.3.7.RELEASE</spring-boot.version>
        <log4j-over-slf4j.version>1.7.29</log4j-over-slf4j.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-cloud-alibaba.version>2.2.6.RELEASE</spring-cloud-alibaba.version>
        <redis.clients.version>2.9.3</redis.clients.version>
        <!--这个版本dubbo 2.7.8 不能用高于1.0.10版本的, dubbo 2.7.15 可以使用1.0.11 -->
        <spring-support-alibaba.version>1.0.11</spring-support-alibaba.version><!--1.0.11-->
        <com.howbuy.util.version>1.0.0-SNAPSHOT</com.howbuy.util.version>
        <com.howbuy.tms.tms-common-log-pattern.version>1.0.0-RELEASE</com.howbuy.tms.tms-common-log-pattern.version>
        <com.howbuy.crm-wechat.version>2.0.4.2-RELEASE</com.howbuy.crm-wechat.version>
        <xstream.version>1.4</xstream.version>
	<com.howbuy.crm-core-client.version>1.9.2.4-RELEASE</com.howbuy.crm-core-client.version>
<com.howbuy.acc-center-facade.version>********-RELEASE</com.howbuy.acc-center-facade.version>
<com.howbuy.crm-nt-client.version>1.9.2.4-RELEASE</com.howbuy.crm-nt-client.version>
<com.howbuy.message-public-client.version>5.1.13-RELEASE</com.howbuy.message-public-client.version>
        <com.howbuy.howbuy-commons-validator.version>1.0.0-SNAPSHOT</com.howbuy.howbuy-commons-validator.version>
        <com.howbuy.crm-account.version>bugfix20250711c-RELEASE</com.howbuy.crm-account.version>
        <com.howbuy-boot-actuator>1.1.6-RELEASE</com.howbuy-boot-actuator>
<com.howbuy.crm-account-client.version>bugfix20250711c-RELEASE</com.howbuy.crm-account-client.version>
</properties>

    <dependencyManagement>
        <dependencies>
            <!--springboot依赖,通过这个无需继承springboot的父pom-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons.codec}</version>
            </dependency>

            <!--actuator健康监测-->
            <dependency>
                <groupId>com.howbuy.boot</groupId>
                <artifactId>howbuy-boot-actuator</artifactId>
                <version>${com.howbuy-boot-actuator}</version>
            </dependency>
            <!-- 日志脱敏 -->
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-log-pattern</artifactId>
                <version>${com.howbuy.tms.tms-common-log-pattern.version}</version>
            </dependency>
            <!--显示声明dubbo版本 也可以直接继承spring Cloud Alibaba 的dubbo Start 一样是2.7.8-->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-dependencies-zookeeper</artifactId>
                <version>${dubbo.version}</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.alibaba.spring</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring-support-alibaba.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.2</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!--log-->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>${disruptor.version}</version>
            </dependency>

            <!--howbuy-->
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>utils</artifactId>
                <version>${com.howbuy.util.version}</version>
            </dependency>
            
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>2.9.3</version>
            </dependency>

            <!--本项目依赖包-->
            <dependency>
                <groupId>com.howbuy.crm</groupId>
                <artifactId>crm-wechat-dao</artifactId>
                <version>${com.howbuy.crm-wechat.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.crm</groupId>
                <artifactId>crm-wechat-service</artifactId>
                <version>${com.howbuy.crm-wechat.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.crm</groupId>
                <artifactId>crm-wechat-client</artifactId>
                <version>${com.howbuy.crm-wechat.version}</version>
            </dependency>

            <dependency>
                <artifactId>howbuy-commons-validator</artifactId>
                <groupId>com.howbuy.commons.validator</groupId>
                <version>${com.howbuy.howbuy-commons-validator.version}</version>
            </dependency>


        </dependencies>

    </dependencyManagement>

    <modules>
        <module>crm-wechat-client</module>
        <module>crm-wechat-remote</module>
        <module>crm-wechat-dao</module>
        <module>crm-wechat-service</module>
    </modules>

    <distributionManagement>
        <repository>
            <id>howbuy-release</id>
            <name>howbuy-release</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshot</id>
            <name>howbuy-snapshot</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>

</project>