package com.howbuy.crm.wechat.service.outerservice.wechatapi.sendwechat;

import cn.hutool.http.HttpUtil;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.WeChatCommonOuterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * @classname: SendMsgServer
 * @author: yu.zhang
 * @description: 发送企业微信消息接口
 * @creatdate: 2021-02-10 11:00
 * @since: JDK1.8
 */
@Slf4j
@Service
@Transactional
public class WechatMessageSendOuterServer {

    @Autowired
    private WeChatCommonOuterService weChatCommonOuterService;

    /**
     * @description:(请在此添加描述)
     * @param paramMap
     * @param corpId
     * @param secret
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/27 14:59
     * @since JDK 1.8
     */
    public void sendMsgByType(Map<String, Object> paramMap, String corpId, String secret) {
        log.info("发送消息sendMsgByType开始:" + paramMap);
        try {

            paramMap.put("enable_duplicate_check", "0");
            paramMap.put("duplicate_check_interval", "30");

            if (StringUtils.isNotEmpty(secret)) {
                StringBuilder tokenUrl = new StringBuilder();
                tokenUrl.append(Constants.INTERACT_DOMAIN_URL).append(String.format(Constants.TOKEN_PATH, corpId, secret));
                String token = weChatCommonOuterService.getToken(tokenUrl.toString());

                StringBuilder sendUrl = new StringBuilder();
                sendUrl.append(Constants.INTERACT_DOMAIN_URL).append(String.format(Constants.MSG_PATH, token));

                String resp = HttpUtil.post(sendUrl.toString(), paramMap);
                log.info("获取到的token:{},请求数据:{},发送微信的响应数据:{}", token, paramMap, resp);
            }
        } catch (Exception e) {
            log.error("发送消息sendMsgByType Exception:{}", e.getMessage());
        }
    }

}
