/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.controller;

import com.google.common.base.Throwables;
import com.howbuy.crm.wechat.dao.po.custrelation.CustConsultRelationPO;
import com.howbuy.crm.wechat.dao.vo.custrelation.CustConsultRelationVO;
import com.howbuy.crm.wechat.service.cacheservice.CacheKeyPrefix;
import com.howbuy.crm.wechat.service.cacheservice.lock.LockService;
import com.howbuy.crm.wechat.service.service.custrelation.WechatCustRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description: (企业微信  客户关系 controller )
 * <AUTHOR>
 * @date 2023/11/24 19:23
 * @since JDK 1.8
 */
@Slf4j
@RestController
@RequestMapping("/wechatcustrelation")
public class WechatCustRelationController {

    @Autowired
    private WechatCustRelationService wechatCustRelationService;

    @Autowired
    protected LockService lockService;

    /**
     * @api {POST} /wechatcustrelation/selectrelationlistbyvo selectRelationListByVo()
     * @apiVersion 1.0.0
     * @apiGroup WechatCustRelationController
     * @apiName selectRelationListByVo()
     * @apiDescription 批量查询客户hboneNo 和 投顾consCode 关系
     * @apiParam (请求体) {Array} requestBody
     * @apiParam (请求体) {String} requestBody.hboneNo 客户信息-一账通号
     * @apiParam (请求体) {String} requestBody.conscode 投顾号
     * @apiParamExample 请求体示例
     * [{"conscode":"t","hboneNo":"AVD8"}]
     * @apiSuccess (响应结果) {Array} response
     * @apiSuccess (响应结果) {Number} response.id id
     * @apiSuccess (响应结果) {String} response.externalUserId 客户信息-外部应用用户ID
     * @apiSuccess (响应结果) {String} response.hboneNo 客户信息-一账通号
     * @apiSuccess (响应结果) {String} response.unionid 客户信息-微信UnionId
     * @apiSuccess (响应结果) {String} response.conscode 投顾号
     * @apiSuccess (响应结果) {String} response.status 状态1新增  2删除客户   3被客户删除
     * @apiSuccess (响应结果) {Number} response.addTime 添加时间
     * @apiSuccess (响应结果) {Number} response.delTime 删除时间
     * @apiSuccess (响应结果) {String} response.companyNo 企业编码1好买财富2好买基金
     * @apiSuccess (响应结果) {String} response.addWay 添加客户的来源
     * @apiSuccess (响应结果) {String} response.state 添加客户的渠道
     * @apiSuccessExample 响应结果示例
     * [{"unionid":"UOHjML","addTime":2995865321154,"companyNo":"SBWNV","externalUserId":"ucxnUUut6","addWay":"j","delTime":1351503081579,"id":1390,"state":"1xRF2","conscode":"o1","hboneNo":"oVgbtOYf","status":"GZVHN"}]
     */
    @PostMapping("/selectrelationlistbyvo")
    @ResponseBody
    public List<CustConsultRelationPO> selectRelationListByVo(@RequestBody List<CustConsultRelationVO> voList){
        return wechatCustRelationService.selectRelationListByVo(voList);
    }

    /**
     * @api {GET} /wechatcustrelation/updateallcustinfolist updateAllCustInfoList()
     * @apiVersion 1.0.0
     * @apiGroup WechatCustRelationController
     * @apiName updateAllCustInfoList()
     * @apiDescription 更新所有高端客户微信信息  和   企业微信客户投顾关联关系
     * @apiSuccess (响应结果) {String} response
     * @apiSuccessExample 响应结果示例
     * "3qL76V5U"
     */
    @RequestMapping(value = "/updateallcustinfolist", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public String updateAllCustInfoList() {
        String uniqKey = CacheKeyPrefix.LOCK_KEY_PREFIX + "updateallcustinfolist";
        // 运行锁，只有在获取了锁之后才准许执行
        boolean lockFlag = false;
        try{
            lockFlag = lockService.getLock(uniqKey, 600);
            // 运行锁，只有在获取了锁之后才准许执行
            if(!lockFlag){
                log.info("updateAllCustInfoList|get lock fail,uniqKey:{}",uniqKey);
                return "";
            }
            return wechatCustRelationService.updateAllCustInfoList();
        } catch (Exception e) {
            log.error("updateAllCustInfoList error", Throwables.getStackTraceAsString(e));
        } finally {
            if(lockFlag){
                // 释放定时任务锁，只有在获取锁成功才释放锁
                lockService.releaseLock(uniqKey);
            }
        }
        return "";
    }


}