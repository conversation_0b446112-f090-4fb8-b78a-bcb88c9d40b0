package com.howbuy.crm.wechat.service.repository;

import com.howbuy.common.utils.Assert;
import com.howbuy.crm.wechat.dao.mapper.CmWechatCustInfoMapper;
import com.howbuy.crm.wechat.dao.mapper.customize.CustomizeCmWechatCustInfoMapper;
import com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO;
import com.howbuy.crm.wechat.service.commom.enums.CompanyWechatEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @description: 企业微信客户操作
 * @author: yu.zhang
 * @date: 2023/6/25 15:17 
 * @since JDK 1.8
 * @version: 1.0
 */
@Repository
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class CmWechatCustInfoRepository {
    @Autowired
    private CmWechatCustInfoMapper cmWechatCustInfoMapper;
    @Autowired
    private CustomizeCmWechatCustInfoMapper customizeCmWechatCustInfoMapper;
    @Autowired
    private CommonRepository commonRepository;

    /**
     * @description:新增或修改企业微信客户信息
     * @param wechatCustInfo	
     * @param corpId
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/25 16:05
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void insertCmWechatCustInfo(CmWechatCustInfoPO wechatCustInfo, String corpId){

        Assert.notNull(corpId, "企业ID不能为空！");
        wechatCustInfo.setCompanyNo(CompanyWechatEnum.getCompanyWechatEnum(corpId).getDesc());
        List<CmWechatCustInfoPO> wechatCustList = customizeCmWechatCustInfoMapper.listWechatCustByExternalUserId(wechatCustInfo.getExternalUserId(),wechatCustInfo.getCompanyNo());

        if(CollectionUtils.isNotEmpty(wechatCustList)){
            wechatCustList.forEach(cmWechatCustInfo ->{
                cmWechatCustInfo.setHboneNo(wechatCustInfo.getHboneNo());
                cmWechatCustInfo.setUnionid(wechatCustInfo.getUnionid());
                cmWechatCustInfo.setNickName(wechatCustInfo.getNickName());
                cmWechatCustInfo.setWechatAvatar(wechatCustInfo.getWechatAvatar());
                cmWechatCustInfo.setModifier("sys");
                cmWechatCustInfo.setUpdateTime(new Date());
                cmWechatCustInfoMapper.updateByPrimaryKeySelective(cmWechatCustInfo);
            });
        }else{
            wechatCustInfo.setId(commonRepository.getWechatCustIdBySeq());
            wechatCustInfo.setCreator("sys");
            wechatCustInfo.setCreateTime(new Date());
            cmWechatCustInfoMapper.insertSelective(wechatCustInfo);
        }
    }

    /**
     * @description:根据企业IC和一账通查询客户信息
     * @param hboneNo	
     * @param corpId
     * @return com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO
     * @author: yu.zhang
     * @date: 2023/6/25 16:05
     * @since JDK 1.8
     */
    public CmWechatCustInfoPO getExternalUserByHboneNo(String hboneNo, String corpId){
        Assert.notNull(corpId, "企业ID不能为空！");
        Assert.notNull(hboneNo, "一账通不能为空！");
        return customizeCmWechatCustInfoMapper.getExternalUserByHboneNo(hboneNo,CompanyWechatEnum.getCompanyWechatEnum(corpId).getDesc());
    }

    /**
     * @description:根据外部联系人id和企业Id查询微信客户信息
     * @param externalUserId
     * @param corpId
     * @return com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO
     * <AUTHOR>
     * @date 2024/9/10 13:22
     * @since JDK 1.8
     */
    public CmWechatCustInfoPO getWechatCustByExternalUserId(String externalUserId, String corpId){
        Assert.notNull(corpId, "企业ID不能为空！");
        Assert.notNull(externalUserId, "外部联系人Id不能为空！");
        return customizeCmWechatCustInfoMapper.getWechatCustByExternalUserId(externalUserId, CompanyWechatEnum.getCompanyWechatEnum(corpId).getDesc());
    }


    /**
     * @description 更新一帐通(账户中心mq接受时使用)
     * @param unionId
     * @param hbOneNo
     * @return
     * <AUTHOR>
     * @date 2024/6/19 3:19 PM
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int updateHbOneNoByUnionId(String hbOneNo, String unionId) {
        return cmWechatCustInfoMapper.updateHbOneNoByUnionId(unionId, hbOneNo);
    }

}
