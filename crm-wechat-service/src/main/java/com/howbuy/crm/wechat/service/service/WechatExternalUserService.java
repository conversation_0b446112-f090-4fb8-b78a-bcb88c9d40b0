/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service;

import com.howbuy.crm.wechat.service.domain.externaluser.ExternalUserInfoDTO;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.WechatExternalContactOuterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/11/1 13:23
 * @since JDK 1.8
 */

@Slf4j
@Service
public class WechatExternalUserService {

    @Autowired
    private WechatExternalContactOuterService wechatExternalContactOuterService;

    /**
     * @description:* 获取客户信息
     * @param externalUserId	NOT NULL 外部联系人的userid
     * @param companyNo  企业应用ID，获取哪个企业的外部联系人
     * @return com.howbuy.crm.wechat.service.domain.externaluser.ExternalUserInfoDTO
     * @author: jin.wang03
     * @date: 2023/11/1 13:39
     * @since JDK 1.8
     */
    public ExternalUserInfoDTO getExternalUser(String externalUserId, String companyNo) {
        return wechatExternalContactOuterService.getExternalUser(externalUserId, companyNo);
    }
}