package com.howbuy.crm.wechat.service.service.group;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.Assert;
import com.howbuy.crm.wechat.service.business.syncchatgroupuser.SyncChatGroupUserBusiness;
import com.howbuy.crm.wechat.service.commom.constant.WxTempConstant;
import com.howbuy.crm.wechat.service.commom.enums.CompanyWechatEnum;
import com.howbuy.crm.wechat.service.domain.callback.ChatEventDTO;
import com.howbuy.crm.wechat.service.repository.CmWechatGroupRepository;
import com.howbuy.crm.wechat.service.repository.CmWechatGroupUserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @description: 群回调处理service
 * @author: yu.zhang
 * @date: 2023/6/12 10:17 
 * @since JDK 1.8
 * @version: 1.0
 */
@Slf4j
@Service
public class ChangeChatEventService {

    @Autowired
    private CmWechatGroupRepository cmWechatGroupRepository;

    @Autowired
    private CmWechatGroupUserRepository cmWechatGroupUserRepository;
    @Autowired
    private SyncChatGroupUserBusiness syncChatGroupUserBusiness;

    public static final String LOG_START_STR = "触发【群回调-{}】事件 START：{}";
    public static final String LOG_END_STR = "触发【群回调-{}】事件 END：{}";

    /**
     * @description:群回调事件
     * @param chatEventDTO
     * @return void
     * @author: shuai.zhang
     * @date: 20240321
     * @since JDK 1.8
     */
    public void processChatChangeType(ChatEventDTO chatEventDTO) {
        Assert.notNull(chatEventDTO.getCorpId(), "企业ID不能为空！");
        switch (chatEventDTO.getChangeType()) {
            case WxTempConstant.CHAT_CHANGE_TYPE_CREATE:
                // 群变更类型-客户群创建事件
                this.handleCreateChatChangeType(chatEventDTO);
                break;
            case WxTempConstant.CHAT_CHANGE_TYPE_UPDATE:
                // 群变更类型-客户群变更事件
                this.handleUpdateChatChangeType(chatEventDTO);
                break;
            case WxTempConstant.CHAT_CHANGE_TYPE_DISMISS:
                // 群变更类型-客户群解散事件
                this.handleDeleteChatChangeType(chatEventDTO);
                break;
            default:
                log.info("processChatChangeType changeType不触发数据处理:{}", JSON.toJSONString(chatEventDTO));
        }
    }

    /**
     * @description:(群变更类型-客户群解散事件)
     * @param chatEventDTO
     * @return void
     * @author: shuai.zhang
     * @date: 2024/3/21 14:17
     * @since JDK 1.8
     */
    private void handleDeleteChatChangeType(ChatEventDTO chatEventDTO) {
        log.info(LOG_START_STR, "客户群解散事件", chatEventDTO);
        /**
         * 当客户群被群主解散后，回调该事件。
         * 需注意的是，如果发生群信息变动，会立即收到此事件，但是部分信息是异步处理，
         * 可能需要等一段时间(例如2秒)调用获取客户群详情接口才能得到最新结果
         */
        cmWechatGroupRepository.deleteByChatId(chatEventDTO.getChatId(), new Date());
        cmWechatGroupUserRepository.deleteByChatId(chatEventDTO.getChatId(), new Date());
        log.info(LOG_END_STR, "客户群解散事件", chatEventDTO);
    }

    /**
     * @description:(群变更类型-客户群变更事件)
     * @param chatEventDTO
     * @return void
     * @author: shuai.zhang
     * @date: 2024/3/21 14:16
     * @since JDK 1.8
     */
    private void handleUpdateChatChangeType(ChatEventDTO chatEventDTO) {
        log.info(LOG_START_STR, "客户群变更事件", chatEventDTO);
        /**
         * 变更详情。目前有以下几种：
         * add_member : 成员入群
         * del_member : 成员退群
         * change_owner : 群主变更
         * change_name : 群名变更
         * change_notice : 群公告变更
         * 但是企微给过来的只有群成员的id, 所以还是要整体同步整个群的信息才行
         */
        try {
            syncChatGroupUserBusiness.syncGroupChatUserByCompanyNoAndChatId(
                    CompanyWechatEnum.getCompanyWechatEnum(chatEventDTO.getCorpId()).getDesc()
                    , chatEventDTO.getChatId(), chatEventDTO.getCreateTime());
        } catch (Exception e) {
            log.error("拉取企微客户群的详情出现异常！", e);
        }
        log.info(LOG_END_STR, "客户群变更事件", chatEventDTO);
    }

    /**
     * @description:(群变更类型-客户群创建事件)
     * @param chatEventDTO
     * @return void
     * @author: shuai.zhang
     * @date: 2024/3/21 14:15
     * @since JDK 1.8
     */
    private void handleCreateChatChangeType(ChatEventDTO chatEventDTO) {
        log.info(LOG_START_STR, "客户群创建事件", chatEventDTO);
        try {
            syncChatGroupUserBusiness.syncGroupChatUserByCompanyNoAndChatId(
                    CompanyWechatEnum.getCompanyWechatEnum(chatEventDTO.getCorpId()).getDesc()
                    , chatEventDTO.getChatId(), null);
        } catch (Exception e) {
            log.error("拉取企微客户群的详情出现异常！", e);
        }
        log.info(LOG_END_STR, "客户群创建事件", chatEventDTO);
    }

}
