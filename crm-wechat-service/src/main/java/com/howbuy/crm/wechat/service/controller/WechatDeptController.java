package com.howbuy.crm.wechat.service.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.wechat.service.service.WechatFullDeptDataScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * Created by shucheng on 2022/1/19 11:12
 */
@Slf4j
@Controller
@RequestMapping("/wechatdept")
public class WechatDeptController {

    @Autowired
    private WechatFullDeptDataScheduleService wechatFullDeptDataScheduleService;


    /**
     * @api {GET} /wechatdept/execute executeScheduleService()
     * @apiVersion 1.0.0
     * @apiGroup WechatDeptController
     * @apiName executeScheduleService()
     * @apiDescription 手动执行任务（产线使用compareInsertAll）
     * @apiSuccess (响应结果) {String} response
     * @apiSuccessExample 响应结果示例
     * "jVOxpVGaVl"
     */
    @ResponseBody
    @GetMapping("/execute")
    public String executeScheduleService(/*String handleType*/) {
        JSONObject taskJson = new JSONObject();
        JSONObject taskParam = new JSONObject();
        taskParam.put("handleType", null);
        taskJson.put("taskParam", taskParam);
        wechatFullDeptDataScheduleService.execute();
        return "success";
    }
}
