/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.syncchatgroupuser;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.howbuy.crm.wechat.dao.po.CmWechatGroupPO;
import com.howbuy.crm.wechat.service.business.syncchatgroupuser.SyncChatGroupUserBusiness;
import com.howbuy.crm.wechat.service.commom.enums.CompanyWechatEnum;
import com.howbuy.crm.wechat.service.commom.utils.LoggerUtils;
import com.howbuy.crm.wechat.service.repository.CmWechatGroupRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * @description: 同步所有企微客户群的成员信息
 * <AUTHOR>
 * @date 2023/10/30 14:33
 * @since JDK 1.8
 */

@Slf4j
@Service
public class SyncChatGroupUserService {

    @Autowired
    private SyncChatGroupUserBusiness syncChatGroupUserBusiness;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolExecutor;

    @Autowired
    private CmWechatGroupRepository cmWechatGroupRepository;


    /**
     * @description: 拉取企微客户群的详情，并写入到数据库中
     * @author: jin.wang03
     * @date: 2023/10/26 10:27
     * @since JDK 1.8
     */
    public void syncChatGroupUserData(String arg) {
        log.info("[拉取企微客户群的详情]-任务开始！>>>>>>>>>>>>>>");

        List<CmWechatGroupPO> groupPoList = parseArg(arg);
        if (CollectionUtils.isEmpty(groupPoList)) {
            log.info("没有需要同步的客户群详情！");
            return;
        }

        String uuid = LoggerUtils.getUuid();
        CountDownLatch countDownLatch = new CountDownLatch(groupPoList.size());

        for (CmWechatGroupPO groupPo : groupPoList) {
            threadPoolExecutor.execute(() -> syncGroupChatUser(groupPo.getCompanyNo(), groupPo.getChatId(), uuid, countDownLatch));
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(e);
        }

        log.info("[拉取企微客户群的详情]-任务结束！>>>>>>>>>>>>>>");
    }

    /**
     * @description: 拉取企微客户群的详情，并写入到数据库中
     * @param companyNo	公司编码
     * @param chatId 客户群ID
     * @param uuid 日志uuid
     * @param countDownLatch 线程计数器
     * @author: jin.wang03
     * @date: 2023/10/31 17:08
     * @since JDK 1.8
     */
    private void syncGroupChatUser(String companyNo, String chatId, String uuid, CountDownLatch countDownLatch) {
        LoggerUtils.setChildUUID(uuid);
        try {
            //此处退群时间 根据任务时间来
            syncChatGroupUserBusiness.syncGroupChatUserByCompanyNoAndChatId(companyNo, chatId, String.valueOf(System.currentTimeMillis()/1000));
        } catch (Exception e) {
            log.error("拉取企微客户群的详情出现异常！", e);
        } finally {
            countDownLatch.countDown();
            LoggerUtils.clearConfig();
        }
    }

    /**
     * @description: 解析入参 获取公司编码列表  默认为财富公司.支持传入chatId和companyNo，如果传入了chatId，则以chatId为准，否则以companyNo为准
     * @param arg 入参
     * @return java.util.List<CmWechatGroupPo> 公司编码列表
     * @author: jin.wang03
     * @date: 2023/10/31 9:15
     * @since JDK 1.8
     */
    private List<CmWechatGroupPO> parseArg(String arg) {
        List<CmWechatGroupPO> cmWechatGroupPOS;

        if (StringUtils.isNotBlank(arg)) {
            JSONObject argJson = JSON.parseObject(arg);
            if (StringUtils.isNotEmpty(argJson.getString("chatId"))) {
                cmWechatGroupPOS = cmWechatGroupRepository.listByChatId(Lists.newArrayList(argJson.getString("chatId").split(",")));
            } else if (StringUtils.isNotEmpty(argJson.getString("companyNo"))) {
                cmWechatGroupPOS = cmWechatGroupRepository.listByCompanyNo(Lists.newArrayList(argJson.getString("companyNo").split(",")));
            } else {
                cmWechatGroupPOS = cmWechatGroupRepository.listByCompanyNo(Lists.newArrayList(CompanyWechatEnum.WEALTH_COMPANY.getDesc()));
            }
        } else {
            cmWechatGroupPOS = cmWechatGroupRepository.listByCompanyNo(Lists.newArrayList(CompanyWechatEnum.WEALTH_COMPANY.getDesc()));
        }

        return cmWechatGroupPOS;
    }
}