package com.howbuy.crm.wechat.service.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.howbuy.crm.wechat.service.repository.CmWechatCustInfoRepository;
import com.howbuy.message.MessageService;
import com.howbuy.message.SimpleMessage;
import com.howbuy.message.processor.MessageProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 * @description: 外部账号绑定及解绑接口实现类
 * @author: jianyi.tao
 * @create: 2022/09/05 10:35
 * @since: JDK 1.7
 */
@Slf4j
@Component("mqOuterAcctListener")
public class MqOuterAcctListenerMessage extends MessageProcessor implements InitializingBean {
    @Autowired
    private CmWechatCustInfoRepository cmWechatCustInfoRepository;

    @Value("${sync.TOPIC_OUTER_ACCT}")
	private String syncDepositQueue;

    @Value("${GLOBAL_HB_WEBCHAT}")
    private String wewbchatUrl;

    // 外部账号绑定 tag
    public static final String OUTER_ACCT_LOGIN_BIND = "OUTER_ACCT_LOGIN_BIND";

    // 外部账号解绑 tag
    public static final String OUTER_ACCT_LOGIN_UNBIND = "OUTER_ACCT_LOGIN_UNBIND";

    @Override
    public void afterPropertiesSet() {
        MessageService.getInstance().addMessageProcessor(syncDepositQueue, this);
    }

    @Override
    public void processMessage(SimpleMessage simpleMessage) {
        try {
            String contentMessage = (String) simpleMessage.getContent();
            log.info("[MqOuterAcctListenerMessage]crm-wechat接收mq进行客户信息更新：{}", contentMessage);
            onMessageByte(contentMessage);
        } catch (Exception e) {
            log.error("[MqOuterAcctListenerMessage]crm-wechat接收mq进行客户信息更新error：{}", Throwables.getStackTraceAsString(e));
        }
    }

    public void onMessageByte(String contentMessage) {
        try {
            // 解析json
            JSONObject contentObject = JSON.parseObject(contentMessage);
            JSONObject headObject = contentObject.getJSONObject("head");
            String bodyMessage = contentObject.getString("body");

            // 获取消息内容
            String tag = headObject.getString("tag");
            JSONObject bodyObject = JSON.parseObject(bodyMessage);
            String hboneNo = replaceNullStr(bodyObject.get("hboneNo"));
            String outerAcct = replaceNullStr(bodyObject.get("outerAcct"));

            // 如果外部账号为空，则跳过执行
            if (StringUtils.isEmpty(outerAcct)) {
                return;
            }

            // 绑定外部账号
            if (OUTER_ACCT_LOGIN_BIND.equals(tag)) {
                int num1 = cmWechatCustInfoRepository.updateHbOneNoByUnionId(hboneNo, outerAcct);
                log.info("【MqOuterAcctListenerMessage】更新unionid = {} 的一帐通号{}成功条数:{}", outerAcct, hboneNo, num1);
            }

            // 解绑外部账号
            if (OUTER_ACCT_LOGIN_UNBIND.equals(tag)) {
                int num2 = cmWechatCustInfoRepository.updateHbOneNoByUnionId("", outerAcct);
                log.info("[MqOuterAcctListenerMessage]解除unionid={}的一帐通号成功条数：{}", outerAcct, num2);
            }
        } catch (Exception e) {
            log.error("[MqOuterAcctListenerMessage]接收账户中心mq进行一帐通更新失败:{}", Throwables.getStackTraceAsString(e));
        }
    }


    public static String replaceNullStr(Object str) {
        if (str == null) {
            return "";
        }
        return str.toString();
    }

}