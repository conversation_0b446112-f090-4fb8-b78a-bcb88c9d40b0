package com.howbuy.crm.wechat.service.domain.callback;

import lombok.*;

import java.io.Serializable;

/**
 * @description: 企业微信回调传参
 * @author: yu.zhang
 * @date: 2023/6/8 10:32 
 * @since JDK 1.8
 * @version: 1.0
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class WechatCallbackDTO implements Serializable {
    /**
     *用于计算签名
     */
    private String token;
    /**
     * 应用ID
     */
    private String corpId;
    /**
     *应用回调自定义消息内容加密
     */
    private String encodingAESKey;

}
