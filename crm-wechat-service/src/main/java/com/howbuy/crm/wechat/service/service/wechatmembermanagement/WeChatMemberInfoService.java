/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.wechatmembermanagement;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.commom.enums.CompanyWechatEnum;
import com.howbuy.crm.wechat.service.domain.wechatmember.WeChatMemberInfoDTO;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.WeChatCommonOuterService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.HashMap;
import java.util.Map;

/**
 * @description: 企业微信成员查询
 * <AUTHOR>
 * @date 2024/6/20 13:43
 * @since JDK 1.8
 */
@Service
public class WeChatMemberInfoService {

    @Autowired
    private WeChatCommonOuterService weChatCommonOuterService;

    public JSONObject queryWeChatMemberInfo(String userId) {
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("userid",userId);
        String resultJson = weChatCommonOuterService.requestInteractRespWithCacheAccessToken(Constants.GET_MEMBER_INFO, CompanyWechatEnum.WEALTH_COMPANY.getKey(), paramsMap, Constants.METHOD_GET);
        return JSON.parseObject(resultJson);
    }

}
