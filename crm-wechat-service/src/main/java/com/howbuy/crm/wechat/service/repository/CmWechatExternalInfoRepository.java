package com.howbuy.crm.wechat.service.repository;

import com.howbuy.common.utils.Assert;
import com.howbuy.crm.wechat.dao.mapper.CmWechatExternalInfoMapper;
import com.howbuy.crm.wechat.dao.mapper.customize.CustomizeCmWechatExternalInfoMapper;
import com.howbuy.crm.wechat.dao.po.CmWechatExternalInfoPO;
import com.howbuy.crm.wechat.service.commom.enums.CompanyWechatEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @description: 企业微信外部联系人
 * @author: yu.zhang
 * @date: 2023/6/12 19:51 
 * @since JDK 1.8
 * @version: 1.0
 */
@Repository
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class CmWechatExternalInfoRepository {

    @Autowired
    private CmWechatExternalInfoMapper cmWechatExternalInfoMapper;

    @Autowired
    private CustomizeCmWechatExternalInfoMapper customizeCmWechatExternalInfoMapper;

    @Autowired
    private CommonRepository commonRepository;

    /**
     * @description:查询企业微信添加的外部联系人信息
     * @param externalUserID
     * @param changeType
     * @param companyNo
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatExternalInfoPO>
     * @author: yu.zhang
     * @date: 2023/6/12 20:08
     * @since JDK 1.8
     */
    public List<CmWechatExternalInfoPO> listCmWechatExternalInfo(String externalUserID, String changeType, String companyNo) {
        return customizeCmWechatExternalInfoMapper.listCmWechatExternal(externalUserID, changeType, companyNo);
    }

    /**
     * @description:添加企业微信外部联系人
     * @param wechatExternalInfo
     * @param corpId
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/25 13:48
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void insertWechatExternal(CmWechatExternalInfoPO wechatExternalInfo, String corpId) {
        Assert.notNull(corpId, "企业ID不能为空！");
        wechatExternalInfo.setId(commonRepository.getWechatExternalIdBySeq());
        wechatExternalInfo.setCreateTime(new Date());
        wechatExternalInfo.setCompanyNo(CompanyWechatEnum.getDesc(corpId));
        cmWechatExternalInfoMapper.insertSelective(wechatExternalInfo);
    }
}
