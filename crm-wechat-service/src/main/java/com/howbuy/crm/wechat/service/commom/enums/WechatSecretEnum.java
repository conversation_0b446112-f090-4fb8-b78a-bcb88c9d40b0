/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.commom.enums;

import com.howbuy.crm.wechat.client.enums.WechatAppEnum;
import com.howbuy.crm.wechat.service.config.WechatConfig;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description: 企微应用秘钥枚举
 * @date 2024/9/19 13:45
 * @since JDK 1.8
 */
public enum WechatSecretEnum {

    WEALTH_CUSTOMER(WechatAppEnum.WEALTH_CUSTOMER.getKey(), WechatConfig.wealthCorpSecret, WechatConfig.wealthCorpid, ""),

    WEALTH_CRM(WechatAppEnum.WEALTH_CRM.getKey(), WechatConfig.crmCorpSecret, WechatConfig.wealthCorpid, ""),

    WEALTH_RHSLT(WechatAppEnum.WEALTH_RHSLT.getKey(), WechatConfig.rhsltCorpSecret, WechatConfig.wealthCorpid, ""),

    WEALTH_PORTRAIT(WechatAppEnum.WEALTH_PORTRAIT.getKey(), WechatConfig.wealthPortraitSecret, WechatConfig.wealthCorpid, WechatConfig.wealthPortraitAgentId),

    FUND_CUSTOMER(WechatAppEnum.FUND_CUSTOMER.getKey(), WechatConfig.fundCorpSecret, WechatConfig.fundCorpid, ""),

    ;

    private String key;
    private String secret;
    private String corpId;
    private String agentId;

    WechatSecretEnum(String key, String secret, String corpId, String agentId) {
        this.key = key;
        this.secret = secret;
        this.corpId = corpId;
        this.agentId = agentId;
    }

    public String getKey() {
        return key;
    }

    public String getSecret() {
        return secret;
    }

    public String getCorpId() {
        return corpId;
    }

    public String getAgentId() {
        return agentId;
    }

    /**
     * @description:根据key获取枚举
     * @param key
     * @return com.howbuy.crm.wechat.service.commom.enums.WechatSecretEnum
     * <AUTHOR>
     * @date 2024/9/19 16:19
     * @since JDK 1.8
     */
    public static WechatSecretEnum getEnumByKey(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

}
