/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.wechatjssdk;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.domain.request.wechatjssdk.GetConfigSignatureRequest;
import com.howbuy.crm.wechat.client.domain.response.wechatjssdk.GetConfigSignatureVO;
import com.howbuy.crm.wechat.service.commom.enums.WechatSecretEnum;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.WeChatCommonOuterService;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.domain.WechatSignatureDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 微信JS SDK服务
 * @date 2024/9/19 10:10
 * @since JDK 1.8
 */
@Slf4j
@Service
public class WechatJsSdkService {

    @Resource
    private WeChatCommonOuterService weChatCommonOuterService;

    /**
     * @description:获取企微配置签名
     * @param request
     * @return com.howbuy.crm.wechat.client.base.Response<com.howbuy.crm.wechat.client.domain.response.wechatjssdk.GetAgentConfigSignatureVO>
     * <AUTHOR>
     * @date 2024/9/19 17:15
     * @since JDK 1.8
     */
    public Response<GetConfigSignatureVO> getConfigSignature(GetConfigSignatureRequest request) {
        WechatSecretEnum wechatSecretEnum = WechatSecretEnum.getEnumByKey(request.getWechatAppEnumKey());
        Assert.notNull(wechatSecretEnum, "企微应用秘钥枚举转换为空");

        GetConfigSignatureVO response = new GetConfigSignatureVO();
        WechatSignatureDTO wechatSignatureDTO = weChatCommonOuterService.getAgentConfigSignatureDTO(wechatSecretEnum, request.getUrl());

        response.setNonceStr(wechatSignatureDTO.getNonceStr());
        response.setTimestamp(wechatSignatureDTO.getTimestamp());
        response.setSignature(wechatSignatureDTO.getSignature());

        response.setAppNonceStr(wechatSignatureDTO.getAppNonceStr());
        response.setAppTimestamp(wechatSignatureDTO.getAppTimestamp());
        response.setAppSignature(wechatSignatureDTO.getAppSignature());

        return Response.ok(response);
    }

}
