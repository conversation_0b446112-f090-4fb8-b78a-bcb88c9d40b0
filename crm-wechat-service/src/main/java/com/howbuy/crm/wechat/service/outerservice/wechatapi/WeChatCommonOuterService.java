package com.howbuy.crm.wechat.service.outerservice.wechatapi;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.crm.wechat.client.enums.WechatTicketType;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.commom.enums.WechatCorpSecretEnum;
import com.howbuy.crm.wechat.service.commom.enums.WechatSecretEnum;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.domain.WechatSignatureDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.security.MessageDigest;
import java.util.*;

/**
 * 微信发送消息
 *
 * @classname: WeChatMsgSend
 * @author: yu.zhang
 * @creatdate: 2021-02-10 11:00
 * @since: JDK1.8
 */
@Slf4j
@Service
public class WeChatCommonOuterService {

    private static Gson gson = new Gson();

    /**
     * @param interactPath url相对路径
     * @param corpId       企业微信id
     * @param paramMap     参数
     * @param method       参数 get  post
     * @return java.lang.String
     * @description:与微信服务端交互封装接口 该接口适用于通过corpId及默认的secret 历史代码适配处理
     * <AUTHOR>
     * @date 2024/9/19 13:42
     * @since JDK 1.8
     */
    public String requestInteractRespWithCacheAccessToken(String interactPath, String corpId, Map paramMap, String method) {
        WechatCorpSecretEnum wechatCorpSecretEnum = WechatCorpSecretEnum.getCompanyWechatEnum(corpId);
        if (wechatCorpSecretEnum == null) {
            return Strings.EMPTY;
        }
        // 通过WechatCorpSecretEnum转换为WechatSecretEnum
        if (WechatCorpSecretEnum.WEALTH_COMPANY == wechatCorpSecretEnum) {
            return requestInteractRespWithCacheAccessToken(interactPath, WechatSecretEnum.WEALTH_CUSTOMER, paramMap, method);
        } else {
            return requestInteractRespWithCacheAccessToken(interactPath, WechatSecretEnum.FUND_CUSTOMER, paramMap, method);
        }
    }

    /**
     * 与微信服务端交互封装接口
     * access_token 缓存
     * 注意!!!!!!!!该方法只用来取代使用getToken()方法的请求, token有多个请注意区分
     *
     * @param interactPath url相对路径 NOT NULL
     * @param paramMap     参数
     * @param method       参数 get  post
     * @return
     */
    public String requestInteractRespWithCacheAccessToken(String interactPath, WechatSecretEnum wechatSecretEnum, Map paramMap, String method) {
        Assert.notNull(interactPath, "企业微信API路径不允许为空");

        try {
            int num = 0;
            while (num < 3) {
                num++;
                // 获取缓存token
                String token = getCacheToken(wechatSecretEnum);
                // 非空则继续处理
                if (StringUtils.isNotEmpty(token)) {
                    StringBuilder url = new StringBuilder();
                    url.append(Constants.INTERACT_DOMAIN_URL).append(interactPath)
                            .append("?access_token=").append(token);
                    String jsonData;
                    if (method.equals(Constants.METHOD_GET)) {
                        jsonData = HttpUtil.get(url.toString(), paramMap);
                    } else {
                        jsonData = HttpUtil.createPost(url.toString())
                                .contentType("application/json")
                                .body(JSON.toJSONString(paramMap)).execute().body();
                    }

                    log.info("requestInteractRespWithCacheAccessToken,method:{};url:{};paramMap:{};jsonData:{}", method, url, JSON.toJSONString(paramMap), jsonData);

                    JSONObject mapData = JSON.parseObject(jsonData);
                    String errCode = mapData.getString(Constants.WECHAT_ERR_CODE_KEY);
                    // 错误码为token过期则删除token缓存
                    if (Constants.WECHAT_TOKEN_EXPIRED_CODE.contains(errCode)) {
                        deleteCacheToken(wechatSecretEnum);
                    } else {
                        //除了token过期外的其他情况都正常返回
                        return jsonData;
                    }
                }
            }
        } catch (Exception ex) {
            log.error("requestInteractRespWithAccessToken error:" + ex.getMessage(), ex);
        }
        return Strings.EMPTY;
    }

    /**
     * @param wechatSecretEnum
     * @param bytes
     * @param fileName
     * @param type
     * @return java.lang.String
     * @description:上传素材
     * <AUTHOR>
     * @date 2024/9/19 16:37
     * @since JDK 1.8
     */
    public String uploadMaterial(WechatSecretEnum wechatSecretEnum, byte[] bytes, String fileName, String type) {
        String token = getCacheToken(wechatSecretEnum);
        Assert.hasText(token, "企微token获取失败");

        StringBuilder url = new StringBuilder();
        url.append(Constants.INTERACT_DOMAIN_URL).append(String.format(Constants.WECHAT_MEDIA_UPLOAD, token, type));

        log.info("uploadFile fileName={}, type={}, url:{}", fileName, type, url);

        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        // 构建请求体
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        ByteArrayResource resource = new ByteArrayResource(bytes) {
            @Override
            public String getFilename() {
                // 设置文件名
                return fileName;
            }
        };
        body.add("media", resource);
        body.add("type", type);
        body.add("filename", fileName);
        // 构建请求实体
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        // 发送请求获取响应
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> response = restTemplate.exchange(url.toString(), HttpMethod.POST, requestEntity, String.class);
        String jsonData = response.getBody();
        log.info("uploadMaterial jsonData:{}", jsonData);

        JSONObject mapData = JSON.parseObject(jsonData);
        String errCode = mapData.getString(Constants.WECHAT_ERR_CODE_KEY);
        // 错误码为token过期则删除token缓存
        if (Constants.WECHAT_TOKEN_EXPIRED_CODE.contains(errCode)) {
            deleteCacheToken(wechatSecretEnum);
        }
        return jsonData;
    }

    /**
     * @param url
     * @return com.howbuy.crm.portrait.service.outerservice.wechatapi.domain.AgentConfigSignatureDTO
     * @description:获取企微代理配置签名
     * <AUTHOR>
     * @date 2024/9/4 19:44
     * @since JDK 1.8
     */
    public WechatSignatureDTO getAgentConfigSignatureDTO(WechatSecretEnum wechatSecretEnum, String url) {
        String enterpriseTicket = this.getCacheJsapiTicket(WechatTicketType.ENTERPRISE_TICKET, wechatSecretEnum);
        String appTicket = this.getCacheJsapiTicket(WechatTicketType.APP_TICKET, wechatSecretEnum);

        String nonceStr = RandomStringUtils.randomAlphanumeric(10);
        String timeStamp = Long.toString(System.currentTimeMillis() / 1000);
        log.info("url:{}，corpid:{}，nonceStr：{}，timeStamp：{}", url, wechatSecretEnum.getCorpId(), nonceStr, timeStamp);

        String enterpriseSignature = signature(enterpriseTicket, nonceStr, timeStamp, url);
        String appSignature = signature(appTicket, nonceStr, timeStamp, url);

        WechatSignatureDTO wechatSignatureDTO = new WechatSignatureDTO();
        wechatSignatureDTO.setNonceStr(nonceStr);
        wechatSignatureDTO.setTimestamp(timeStamp);
        wechatSignatureDTO.setSignature(enterpriseSignature);
        wechatSignatureDTO.setAppNonceStr(nonceStr);
        wechatSignatureDTO.setAppTimestamp(timeStamp);
        wechatSignatureDTO.setAppSignature(appSignature);
        return wechatSignatureDTO;
    }

    private String signature(String jsapiTicket, String nonceStr, String timeStamp, String url) {
        SortedMap<String, String> params = new TreeMap<>();
        params.put("noncestr", nonceStr);
        params.put("jsapi_ticket", jsapiTicket);
        params.put("timestamp", timeStamp);
        params.put("url", url);
        String signature = sortSignByASCII(params);
        log.info("step1:待签名参数按照字段名的ASCII码从小到大排序：{}", signature);
        signature = sha1Digest(signature);
        log.info("step2:对string1进行sha1签名，得到signature:{}", signature);
        return signature;
    }


    /**
     * @param wechatSecretEnum
     * @return void
     * @description:根据WechatSecretEnum 获取缓存token
     * <AUTHOR>
     * @date 2024/9/19 13:59
     * @since JDK 1.8
     */
    public String getCacheToken(WechatSecretEnum wechatSecretEnum) {
        String key = Constants.ACCESS_TOKEN_PARTITION + wechatSecretEnum.getKey();
        // 缓存中获取token
        String token = CacheServiceImpl.getInstance().get(key);
        log.info("getInteractRespWithAccessToken,===>redis获取到token:{}", token);
        // 非空，则返回
        if (StringUtils.isNotEmpty(token)) {
            return token;
        }
        // 请求获取新的token
        StringBuilder url = new StringBuilder();
        url.append(Constants.INTERACT_DOMAIN_URL).append(String.format(Constants.TOKEN_PATH, wechatSecretEnum.getCorpId(), wechatSecretEnum.getSecret()));
        String newToken = getToken(url.toString());
        log.info("getInteractRespWithAccessToken,token已过期重新获取token:{}", newToken);
        // 新的token不为空则设置缓存
        if (StringUtils.isNotEmpty(newToken)) {
            CacheServiceImpl.getInstance().put(key, newToken);
        }
        return newToken;
    }

    /**
     * @param wechatSecretEnum
     * @return void
     * @description:根据WechatSecretEnum 删除缓存token
     * <AUTHOR>
     * @date 2024/9/19 14:00
     * @since JDK 1.8
     */
    private void deleteCacheToken(WechatSecretEnum wechatSecretEnum) {
        CacheServiceImpl.getInstance().remove(Constants.ACCESS_TOKEN_PARTITION + wechatSecretEnum.getKey());
        log.info("企微返回42001|40014 access_token已过期,执行删除token");
    }

    /**
     * @param tokenUrl
     * @return java.lang.String
     * @description:微信授权请求，GET类型，获取授权响应，用于其他方法截取token
     * @author: yu.zhang
     * @date: 2023/6/19 13:53
     * @since JDK 1.8
     */
    public String getToken(String tokenUrl) {
        log.info("getToken url={}", tokenUrl);

        String resp = HttpUtil.get(tokenUrl);
        Map<String, Object> map = gson.fromJson(resp, new TypeToken<Map<String, Object>>() {
        }.getType());
        log.info("getToken resp:{}", resp);
        if (map != null && Objects.equals(Constants.WECHAT_ERRMSG, map.get(Constants.WECHAT_ERR_MSG_KEY))
                && Objects.nonNull(map.get("access_token"))) {
            return map.get("access_token").toString();
        } else {
            return Strings.EMPTY;
        }
    }

    /**
     * @description:获取缓存企业JS SDK签名ticket
     * @param wechatTicketType
     * @param wechatSecretEnum
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/9/24 13:30
     * @since JDK 1.8
     */
    public String getCacheJsapiTicket(WechatTicketType wechatTicketType, WechatSecretEnum wechatSecretEnum) {
        String key = Constants.WECHAT_JS_SDK_CACHE_KEY + wechatSecretEnum.getKey() + wechatTicketType.getCode();
        String jsapiTicket = CacheServiceImpl.getInstance().get(key);
        //如果非空，则返回
        if (StringUtils.isNotEmpty(jsapiTicket)) {
            log.info("从缓存读取，类型：{} jsapiTicket:{}", wechatTicketType.getDesc(), jsapiTicket);
            return jsapiTicket;
        }

        // 获取新的ticket
        String newJsapiTicket = getJsapiTicket(wechatTicketType, wechatSecretEnum);

        Assert.hasText(newJsapiTicket, "企微ticket获取失败");

        log.info("getCacheJsapiTicket，类型：{} 获取jsapiTicket:{}", wechatTicketType.getDesc(), newJsapiTicket);
        CacheServiceImpl.getInstance().put(key, newJsapiTicket);
        return newJsapiTicket;
    }

    /**
     * @description:获取JS SDK签名ticket
     * @param wechatTicketType
     * @param wechatSecretEnum
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/9/24 13:30
     * @since JDK 1.8
     */
    private String getJsapiTicket(WechatTicketType wechatTicketType, WechatSecretEnum wechatSecretEnum) {
        Map<String, String> paramsMap = new HashMap<>();
        String ticketPath;
        if (WechatTicketType.APP_TICKET == wechatTicketType) {
            paramsMap.put("type", "agent_config");
            ticketPath = Constants.WECHAT_APP_JS_API_TICKET_PATH;
        } else {
            ticketPath = Constants.WECHAT_ENTERPRISE_JS_API_TICKET_PATH;
        }

        String resp = requestInteractRespWithCacheAccessToken(ticketPath, wechatSecretEnum,
                paramsMap, Constants.METHOD_GET);
        log.info("getTicket 类型：{} resp:{}", wechatTicketType.getDesc(), resp);

        Map<String, Object> map = gson.fromJson(resp, new TypeToken<Map<String, Object>>() {
        }.getType());
        if (map != null && Objects.equals(Constants.WECHAT_ERRMSG, map.get(Constants.WECHAT_ERR_MSG_KEY))
                && Objects.nonNull(map.get("ticket"))) {
            // 成功则返回
            return map.get("ticket").toString();
        }
        return Strings.EMPTY;
    }

    /**
     * @param str
     * @return java.lang.String
     * @description:sha1加密
     * <AUTHOR>
     * @date 2024/9/4 19:41
     * @since JDK 1.8
     */
    public static String sha1Digest(String str) {
        try {
            // SHA1签名生成
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            md.update(str.getBytes());
            byte[] digest = md.digest();

            StringBuffer hexStr = new StringBuffer();
            String shaHex;
            for (int i = 0; i < digest.length; i++) {
                shaHex = Integer.toHexString(digest[i] & 0xFF);
                if (shaHex.length() < 2) {
                    hexStr.append(0);
                }
                hexStr.append(shaHex);
            }
            return hexStr.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @param parameters
     * @return java.lang.String
     * @description:对所有待签名参数按照字段名的ASCII 码从小到大排序
     * <AUTHOR>
     * @date 2024/9/4 19:42
     * @since JDK 1.8
     */
    public static String sortSignByASCII(SortedMap<String, String> parameters) {
        // 以k1=v1&k2=v2...方式拼接参数
        StringBuilder builder = new StringBuilder();
        for (Map.Entry<String, String> s : parameters.entrySet()) {
            String k = s.getKey();
            String v = s.getValue();
            // 过滤空值
            if (StringUtils.isBlank(v)) {
                continue;
            }
            builder.append(k).append("=").append(v).append("&");
        }
        if (!parameters.isEmpty()) {
            builder.deleteCharAt(builder.length() - 1);
        }
        return builder.toString();
    }

}