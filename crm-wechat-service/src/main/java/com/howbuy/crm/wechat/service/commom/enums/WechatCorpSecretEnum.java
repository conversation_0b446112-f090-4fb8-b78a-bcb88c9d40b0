package com.howbuy.crm.wechat.service.commom.enums;

import com.howbuy.crm.wechat.service.config.WechatConfig;
import org.apache.logging.log4j.util.Strings;

import java.util.stream.Stream;

/**
 * @description: 企业微信应用ID
 * @author: yu.zhang
 * @date: 2023/6/12 19:25 
 * @since JDK 1.8
 * @version: 1.0
 */
public enum WechatCorpSecretEnum {

    WEALTH_COMPANY(WechatConfig.wealthCorpid,WechatConfig.wealthCorpSecret,WechatConfig.crmCorpSecret),
    FUND_COMPANY(WechatConfig.fundCorpid,WechatConfig.fundCorpSecret, Strings.EMPTY);

    private final String key;
    private final String desc;
    private final String crmSecret;

    public static WechatCorpSecretEnum getCompanyWechatEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        WechatCorpSecretEnum companyWechatEnum = getCompanyWechatEnum(code);
        return companyWechatEnum == null ? null : companyWechatEnum.getDesc();
    }

    public static String getCrmSecret(String code) {
        WechatCorpSecretEnum companyWechatEnum = getCompanyWechatEnum(code);
        return companyWechatEnum == null ? null : companyWechatEnum.getCrmSecret();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    public String getCrmSecret() {
        return this.crmSecret;
    }

    WechatCorpSecretEnum(String key, String desc, String crmSecret) {
        this.key = key;
        this.desc = desc;
        this.crmSecret = crmSecret;
    }
}
