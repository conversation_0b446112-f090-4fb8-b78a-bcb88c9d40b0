/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.group;

import com.howbuy.crm.wechat.service.business.group.WechatGroupBusiness;
import com.howbuy.crm.wechat.service.domain.externaluser.GroupChatInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: (企微群service)
 * <AUTHOR>
 * @date 2024-3-15 15:27:46
 * @since JDK 1.8
 */

@Slf4j
@Service
public class WechatGroupService {

    @Autowired
    private WechatGroupBusiness wechatGroupBusiness;
    /**
     * @description:(根据员工账号查询所有有他的群)
     * @param userId
     * @return java.util.List<com.howbuy.crm.wechat.service.domain.externaluser.GroupChatInfo>
     * @author: shuai.zhang
     * @date: 2024/3/15 16:25
     * @since JDK 1.8
     */
    public List<GroupChatInfo> getGroupInfoByUserId(String userId) {
        return wechatGroupBusiness.getGroupInfoByUserId(userId);
    }

    /**
     * @description:(根据群ID获取群信息)
     * @param chatId
     * @return com.howbuy.crm.wechat.service.domain.externaluser.GroupChatInfo
     * @author: shuai.zhang
     * @date: 2024/4/2 13:46
     * @since JDK 1.8
     */
    public GroupChatInfo getGroupInfoByChatId(String chatId) {
        return wechatGroupBusiness.getGroupInfoByChatId(chatId);
    }
}