/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.business;

import com.howbuy.cc.message.SendMsgResult;
import com.howbuy.cc.message.email.*;
import com.howbuy.cc.message.phone.SendShortMsgByPhoneResponse;
import com.howbuy.cc.message.phone.SendShortMsgByPhoneService;
import com.howbuy.cc.message.phone.SendShortMsgByTemplateRequest;
import com.howbuy.cc.message.send.company.CompanySendService;
import com.howbuy.cc.message.send.company.SelfAppMessageRequest;
import com.howbuy.cc.message.send.company.SendBotMessageRequest;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/11/3 13:55
 * @since JDK 1.8
 */

@Slf4j
@Component
public class CompanySendBusiness {
//    @DubboReference(registry = Constants.ZK_MESSAGE_PUBLIC, check = false)
    @Autowired
    private CompanySendService companySendService;

    @DubboReference(registry = Constants.ZK_MESSAGE_PUBLIC, check = false)
    private SendEmailService sendMsgService;


    @DubboReference(registry = Constants.ZK_MESSAGE_PUBLIC, check = false)
    private SendShortMsgByPhoneService sendShortMsgByPhoneService;

    @DubboReference(registry = Constants.ZK_MESSAGE_PUBLIC, check = false)
    private SendEmailWithAttachSupportEncryService emailWithAttachService;


    /**
     * @description:(请在此添加描述)
     * @param request
     * @return com.howbuy.cc.message.SendMsgResult
     * @author: jin.wang03
     * @date: 2023/11/3 13:57
     * @since JDK 1.8
     */
    public SendMsgResult sendBotMessage(SendBotMessageRequest request) {
        return companySendService.sendBotMessage(request);
    }

    /**
     * @description 自建应用发送企微消息
     * @param selfAppMessageRequest
     * @return
     * <AUTHOR>
     * @date 2023/11/29 10:48 AM
     * @since JDK 1.8
     */
    public SendMsgResult sendNewApplicationMsg(SelfAppMessageRequest selfAppMessageRequest) {
        return companySendService.sendSelfBuiltApplicationMessage(selfAppMessageRequest);
    }


    /**
     * @description: 发送邮件
     * @param selfAppMessageRequest
     * @return com.howbuy.cc.message.email.SendEmailResponse
     * @author: jin.wang03
     * @date: 2024/5/27 18:11
     * @since JDK 1.8
     */
    public SendEmailResponse sendEmailMsg(SendEmailByTemplateRequest selfAppMessageRequest) {
        return sendMsgService.sendEmailByTemplate(selfAppMessageRequest);
    }


    /**
     * @description: 发送短信
     * @param selfAppMessageRequest
     * @return com.howbuy.cc.message.phone.SendShortMsgByPhoneResponse
     * @author: jin.wang03
     * @date: 2024/5/27 18:12
     * @since JDK 1.8
     */
    public SendShortMsgByPhoneResponse sendSmsMsg(SendShortMsgByTemplateRequest selfAppMessageRequest) {
        return sendShortMsgByPhoneService.sendMsgByTemplate(selfAppMessageRequest);
    }

    /**
     * @description: 发送邮件带附件
     * @param request
     * @return com.howbuy.cc.message.email.SendEmailResponse
     * @author: jin.wang03
     * @date: 2024/10/28 15:37
     * @since JDK 1.8
     */
    public SendEmailResponse sendEmailWithAttachmentMsg(SendEmailWithAttachSupportEncryRequest request) {
        return emailWithAttachService.execute(request);
    }

}