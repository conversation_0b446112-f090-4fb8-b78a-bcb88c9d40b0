/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.job;

import com.howbuy.crm.wechat.service.service.syncchatgroup.SyncChatGroupService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: 根据员工userId同步所有企微客户群定时任务
 * <AUTHOR>
 * @date 2023/10/25 17:26
 * @since JDK 1.8
 */
@Slf4j
@Component
public class SyncChatGroupJob extends AbstractBatchMessageJob {

    /**
     * 调度消息队列，nacos中需要配置sync.SYNC_CHAT_GROUP_JOB对应的队列名称
     */
    @Value("${sync_chat_cust_group_channel:TOPIC_SYNC_CHAT_CUST_GROUP_JOB}")
    private String queue;

    @Autowired
    private SyncChatGroupService syncChatGroupService;

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("SyncChatGroupJob process start");
        try {
            syncChatGroupService.syncChatGroupData(getContent(message));
        } catch (Exception e) {
            log.error("error in SyncChatGroupJob", e);
        }
        log.info("SyncChatGroupJob process end");
    }

    @Override
    protected String getQuartMessageChannel() {
        // 返回调度配置的队列名
        return queue;
    }
}