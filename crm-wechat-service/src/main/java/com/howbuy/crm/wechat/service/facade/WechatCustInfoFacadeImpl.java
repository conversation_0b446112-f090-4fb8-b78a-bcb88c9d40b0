/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.facade;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.facade.wechatcustinfo.WechatCustInfoFacade;
import com.howbuy.crm.wechat.client.domain.request.wechatcustinfo.QueryWechatCustInfoRequest;
import com.howbuy.crm.wechat.client.domain.response.wechatcustinfo.WechatCustInfoVO;
import com.howbuy.crm.wechat.service.service.wechatcustinfo.WechatCustInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 微信客户信息服务实现
 * @date 2024/9/6 11:19
 * @since JDK 1.8
 */
@DubboService
@Slf4j
public class WechatCustInfoFacadeImpl implements WechatCustInfoFacade {

    @Resource
    private WechatCustInfoService wechatCustInfoService;

    @Override
    public Response<WechatCustInfoVO> queryWechatCustInfo(QueryWechatCustInfoRequest request) {
        return Response.ok(wechatCustInfoService.queryWechatCustInfo(request));
    }
}
