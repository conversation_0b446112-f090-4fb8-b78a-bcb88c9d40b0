package com.howbuy.crm.wechat.service.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.wechat.service.commom.aes.WXBizMsgCrypt;
import com.howbuy.crm.wechat.service.config.WechatConfig;
import com.howbuy.crm.wechat.service.domain.callback.WechatCallbackDTO;
import com.howbuy.crm.wechat.service.domain.callback.WechatCallbackSignatureDTO;
import com.howbuy.crm.wechat.service.domain.externaluser.GroupChatInfo;
import com.howbuy.crm.wechat.service.outerservice.messagecenter.CompanySendOuterService;
import com.howbuy.crm.wechat.service.service.WechatCallBackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @classname: WechatTransferEventDealController
 * @author: shuai.zhang
 * @description: 企业微信回调旧企微转至新企微处理contorller
 * @creatdate: 2024-03-19 16:48
 * @since: JDK1.8
 */
@Slf4j
@RestController
@RequestMapping("/wechattransfereventdeal")
public class WechatTransferEventDealController {

    @Autowired
    private WechatCallBackService wechatCallBackService;
    @Autowired
    private CompanySendOuterService companySendOuterService;

    /**
     * @api {POST} /wechattransfereventdeal/transfereventdeal transferEventDeal()
     * @apiVersion 1.0.0
     * @apiGroup WechatTransferEventDealController
     * @apiName transferEventDeal()
     * @apiDescription 企业微信回调旧企微转至新企微处理
     * @apiParam (请求参数) {Object} requestMap
     * @apiParamExample 请求参数示例
     * requestMap={}
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * null
     */
    @PostMapping("/transfereventdeal")
    @ResponseBody
    public String transferEventDeal(@RequestBody Map<String, Object> requestMap) {
        wechatCallBackService.transferEventDeal(requestMap);
        return "success";
    }

    /**
     * @api {POST} /wechattransfereventdeal/testdfile testDfile()
     * @apiVersion 1.0.0
     * @apiGroup WechatTransferEventDealController
     * @apiName testDfile()
     * @apiDescription 测试
     * @apiParam (请求体) {Object} requestBody
     * @apiParamExample 请求体示例
     * {}
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * null
     */
    @PostMapping("/testdfile")
    @ResponseBody
    public void testDfile(@RequestBody Map<String, String> requestMap) throws Exception {
        companySendOuterService.testDfile(requestMap.get("msg"));
    }


    public static void main(String[] args) {
        String a = "{\"filePath\":\"/data/files/crmwechat/goodnews//20240619/901_150400.png\",\"messageType\":4,\"textBotMessage\":{\"content\":\"/:rose /:rose热烈祝贺华南区域深圳三分刘欣，今日成功配置毕盛狮远A类份额 200万[Party][Party]\"},\"webHookUrlKey\":\"06265c29-a683-44fb-bad1-9a7efa78d926\"}";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msg",a);
        System.out.println(jsonObject.toJSONString());
    }
}
