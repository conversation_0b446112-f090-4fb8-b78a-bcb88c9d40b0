package com.howbuy.crm.wechat.service.repository;

import com.howbuy.crm.wechat.dao.mapper.CmWechatRefreshConscustMapper;
import com.howbuy.crm.wechat.dao.po.CmWechatRefreshConscustPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @author: hongdong.xie
 * @date: 2025-02-12
 * @description: 投顾客户微信刷新状态表Repository
 */
@Repository
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class CmWechatRefreshConscustRepository {

    @Autowired
    private CmWechatRefreshConscustMapper cmWechatRefreshConscustMapper;

    /**
     * 更新刷新状态表记录
     * @param po 待更新的记录
     * @return 更新结果
     */
    public int updateByPrimaryKeySelective(CmWechatRefreshConscustPO po) {
        return cmWechatRefreshConscustMapper.updateByPrimaryKeySelective(po);
    }

    /**
     * 获取当天需要处理的记录
     * @param nowDate 当前日期
     * @param dealStatus 处理状态
     * @param dealtype 类型
     * @return 记录列表
     */
    public List<CmWechatRefreshConscustPO> getCmWechatRefreshConscustNowDateDeal(String nowDate, String dealStatus, String dealtype) {
        return cmWechatRefreshConscustMapper.getCmWechatRefreshConscustNowDateDeal(nowDate, dealStatus, dealtype);
    }

    /**
     * 批量插入当天需要处理的记录
     * @param nowDate 当前日期
     * @param dealType 类型
     * @param needRefreshWechatConsCode 需要刷新的投顾编号列表
     * @return 插入记录数
     */
    public int insertCmWechatRefreshConscustNowDate(String nowDate, String dealType, List<String> needRefreshWechatConsCode) {
        return cmWechatRefreshConscustMapper.insertCmWechatRefreshConscustNowDate(nowDate, dealType, needRefreshWechatConsCode);
    }

    /**
     * 更新指定日期和类型的记录状态
     * @param nowDate 当前日期
     * @param dealType 类型
     * @param dealStatus 状态
     * @return 更新记录数
     */
    public int updateCmWechatRefreshConscustStatusByDateAndType(String nowDate, String dealType,String dealStatus) {
        return cmWechatRefreshConscustMapper.updateCmWechatRefreshConscustStatusByDateAndType(nowDate, dealType, dealStatus);
    }
} 