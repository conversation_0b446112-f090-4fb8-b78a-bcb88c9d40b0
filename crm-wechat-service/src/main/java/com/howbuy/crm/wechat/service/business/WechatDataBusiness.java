package com.howbuy.crm.wechat.service.business;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.crm.wechat.dao.po.CmWechatDeptPO;
import com.howbuy.crm.wechat.dao.po.CmWechatEmpPO;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.commom.enums.CompanyWechatEnum;
import com.howbuy.crm.wechat.service.commom.enums.WechatCorpSecretEnum;
import com.howbuy.crm.wechat.service.config.WechatConfig;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.WeChatCommonOuterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @description:企业微信数据获取工具类
 * @author: yu.zhang
 * @date: 2023/6/26 18:23
 * @since JDK 1.8
 */
@Slf4j
@Component
public class WechatDataBusiness {

    @Autowired
    private WeChatCommonOuterService weChatCommonOuterService;
    /**
     * 工号名称
     */
    private static final String EMP_NAME = "工号";

    /**
     * @description:获取全量部门列表数据
     * @param
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatDeptPO>
     * @author: yu.zhang
     * @date: 2023/6/26 18:23
     * @since JDK 1.8
     */
    public List<CmWechatDeptPO> getFullDeptList() {

        List<String> corpIds = Lists.newArrayList(WechatConfig.wealthCorpid, WechatConfig.fundCorpid);
        List<CmWechatDeptPO> resultList = Lists.newArrayList();
        corpIds.forEach(corpId -> {

            StringBuilder url = new StringBuilder();
            url.append(Constants.INTERACT_DOMAIN_URL).append(String.format(Constants.TOKEN_PATH, corpId, WechatCorpSecretEnum.getDesc(corpId)));
            String token = weChatCommonOuterService.getToken(url.toString());

            if (StringUtils.isNotEmpty(token)) {
                StringBuilder requetUrl = new StringBuilder();
                requetUrl.append(Constants.INTERACT_DOMAIN_URL)
                        .append(Constants.GET_DEPT_INFO_LIST)
                        .append("?access_token=").append(token);//强制使用 access_token

                String fullDeptListStr = HttpUtil.get(requetUrl.toString());
                JSONObject jsonObject = JSON.parseObject(fullDeptListStr);
                JSONArray deptJsonArray = jsonObject.getJSONArray("department");
                deptJsonArray.stream().forEach(obj -> {
                    JSONObject deptObj = (JSONObject) obj;
                    CmWechatDeptPO wechatDept = new CmWechatDeptPO();
                    wechatDept.setDeptId((Integer) deptObj.get("id"));
                    wechatDept.setDeptName((String) deptObj.get("name"));
                    wechatDept.setParentDeptId((Integer) deptObj.get("parentid"));
                    wechatDept.setCompanyNo(CompanyWechatEnum.getDesc(corpId));
                    resultList.add(wechatDept);
                });
            }

        });

        return resultList;
    }

    /**
     * @description:获取全量部门成员详情数据
     * @param
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatEmpPO>
     * @author: yu.zhang
     * @date: 2023/6/26 18:23
     * @since JDK 1.8
     */
    public List<CmWechatEmpPO> getFullDeptUserDetailList() {

        List<String> corpIds = Lists.newArrayList(WechatConfig.wealthCorpid, WechatConfig.fundCorpid);
        List<CmWechatEmpPO> resultList = Lists.newArrayList();

        corpIds.forEach(corpId -> {
            StringBuilder url = new StringBuilder();
            url.append(Constants.INTERACT_DOMAIN_URL).append(String.format(Constants.TOKEN_PATH, corpId, WechatCorpSecretEnum.getDesc(corpId)));
            String token = weChatCommonOuterService.getToken(url.toString());

            if (StringUtils.isNotEmpty(token)) {
                StringBuilder requetUrl = new StringBuilder();
                requetUrl.append(Constants.INTERACT_DOMAIN_URL)
                        .append(Constants.GET_USER_DETAIL_LIST)
                        .append("?access_token=").append(token);//强制使用 access_token

                Map<String, Object> paramMap = Maps.newHashMap();
                paramMap.put("department_id", "1");
                paramMap.put("fetch_child", "1");

                String fullDeptUserDetailListStr = HttpUtil.get(requetUrl.toString(), paramMap);
                log.info("getFullDeptUserDetailList|fullDeptUserDetailListStr:{}",fullDeptUserDetailListStr);
                JSONObject jsonObject = JSON.parseObject(fullDeptUserDetailListStr);
                JSONArray userlistJsonArray = jsonObject.getJSONArray("userlist");
                if(CollectionUtils.isNotEmpty(userlistJsonArray)){
                    log.info("userlistJsonArray|size:{}",userlistJsonArray.size());
                    userlistJsonArray.stream().forEach(obj -> {
                        JSONObject userObj = (JSONObject) obj;
                        CmWechatEmpPO wechatEmp = new CmWechatEmpPO();
                        wechatEmp.setEmpId((String) userObj.get("userid"));
                        wechatEmp.setEmpName((String) userObj.get("name"));
                        wechatEmp.setDeptId(userObj.getInteger("main_department"));
                        wechatEmp.setCompanyNo(CompanyWechatEnum.getDesc(corpId));
                        wechatEmp.setEmpWorkId(getEmpWorkId(userObj.getJSONObject("extattr")));
                        wechatEmp.setThumbAvatar((String)userObj.get("thumb_avatar"));
                        wechatEmp.setAvatar((String)userObj.get("avatar"));
                        wechatEmp.setQrCode((String)userObj.get("qr_code"));
                        wechatEmp.setEmail((String)userObj.get("email"));
                        resultList.add(wechatEmp);
                    });
                }
            }
        });

        return resultList;
    }

    /**
     * 获得员工工号
     * @param extattrObject
     * @return
     */
    private String getEmpWorkId(JSONObject extattrObject){
        if(extattrObject.containsKey("attrs")){
            return analysisAttrs(extattrObject.getJSONArray("attrs"));
        }
        return null;
    }

    /**
     * 解析扩展属性中的员工工号
     * @param attrsJsonArray
     * @return
     */
    private String analysisAttrs(JSONArray attrsJsonArray){
        String empWorkId = null;
        for (int i = 0; i < attrsJsonArray.size(); i++) {
            JSONObject attrObj = attrsJsonArray.getJSONObject(i);
            if (!EMP_NAME.equals(attrObj.get("name"))) {
                continue;
            }
            JSONObject textObj = (JSONObject) attrObj.get("text");
            empWorkId = (String) textObj.get("value");
            break;
        }
        return empWorkId;
    }
}
