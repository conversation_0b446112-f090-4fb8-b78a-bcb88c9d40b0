package com.howbuy.crm.wechat.service.repository;

import com.howbuy.common.utils.Assert;
import com.howbuy.crm.wechat.dao.mapper.CmWechatCustRelationMapper;
import com.howbuy.crm.wechat.dao.mapper.customize.CustomizeCmWechatCustRelationMapper;
import com.howbuy.crm.wechat.dao.po.CmWechatCustRelationPO;
import com.howbuy.crm.wechat.service.commom.enums.CompanyWechatEnum;
import com.howbuy.crm.wechat.service.commom.enums.WechatStatusEnum;
import com.howbuy.crm.wechat.service.domain.externaluser.ExternalUserRelationInfoDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @description: crm-wechat 
 * @author: yu.zhang
 * @date: 2023/6/25 13:49 
 * @since JDK 1.8
 * @version: 1.0
 */
@Repository
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class CmWechatCustRelationRepository {

    @Autowired
    private CmWechatCustRelationMapper cmWechatCustRelationMapper;
    @Autowired
    private CustomizeCmWechatCustRelationMapper customizeCmWechatCustRelationMapper;
    @Autowired
    private CommonRepository commonRepository;

    /**
     * @description:(事件处理投顾客户关系)
     * @param wechatCustRelation	
     * @param corpId
     * @return void
     * @author: shuai.zhang
     * @date: 2024/3/27 16:15
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void insertCmWechatCustRelation(CmWechatCustRelationPO wechatCustRelation, String corpId) {
        Assert.notNull(corpId, "企业ID不能为空！");
        wechatCustRelation.setCompanyNo(CompanyWechatEnum.getCompanyWechatEnum(corpId).getDesc());
        CmWechatCustRelationPO cmWechatCustRelation = customizeCmWechatCustRelationMapper.selectByExternalIdAndUserId(wechatCustRelation.getExternalUserId(),
                wechatCustRelation.getConscode(), wechatCustRelation.getCompanyNo());

        if (cmWechatCustRelation == null) {
            wechatCustRelation.setId(commonRepository.getWechatCustIdBySeq());
            wechatCustRelation.setCreator("sys");
            wechatCustRelation.setCreateTime(wechatCustRelation.getCreateTime());
            cmWechatCustRelationMapper.insertSelective(wechatCustRelation);
        } else {
            cmWechatCustRelation.setConscode(wechatCustRelation.getConscode());
            cmWechatCustRelation.setExternalUserId(wechatCustRelation.getExternalUserId());
            cmWechatCustRelation.setStatus(wechatCustRelation.getStatus());
            cmWechatCustRelation.setAddTime(wechatCustRelation.getAddTime());
            cmWechatCustRelation.setDelTime(wechatCustRelation.getDelTime());
            cmWechatCustRelation.setCreator(wechatCustRelation.getCreator());
            cmWechatCustRelation.setCreateTime(wechatCustRelation.getCreateTime());
            cmWechatCustRelation.setUpdateTime(new Date());
            cmWechatCustRelation.setModifier("sys");
            cmWechatCustRelation.setAddWay(wechatCustRelation.getAddWay());
            cmWechatCustRelation.setState(wechatCustRelation.getState());
            cmWechatCustRelation.setCompanyNo(wechatCustRelation.getCompanyNo());
            cmWechatCustRelationMapper.updateByPrimaryKeySelective(cmWechatCustRelation);
        }
    }

    /**
     * @description:(定时任务处理投顾客户关系)
     * @param relationInfo	
     * @param corpId
     * @return void
     * @author: shuai.zhang
     * @date: 2024/3/27 16:15
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void insertCmWechatCustRelationForTask(ExternalUserRelationInfoDTO relationInfo, String corpId) {
        Assert.notNull(corpId, "企业ID不能为空！");
        CmWechatCustRelationPO cmWechatCustRelation = customizeCmWechatCustRelationMapper.selectByExternalIdAndUserId(
                relationInfo.getExternalUserId(),
                relationInfo.getUserid(), CompanyWechatEnum.getCompanyWechatEnum(corpId).getDesc());

        if (cmWechatCustRelation == null) {
            cmWechatCustRelation = new CmWechatCustRelationPO();
            cmWechatCustRelation.setId(commonRepository.getWechatCustIdBySeq());
            cmWechatCustRelation.setConscode(relationInfo.getUserid());
            cmWechatCustRelation.setStatus(WechatStatusEnum.ADD.getKey());
            cmWechatCustRelation.setAddTime(relationInfo.getCreatetime());
            cmWechatCustRelation.setState(relationInfo.getState());
            cmWechatCustRelation.setCompanyNo(CompanyWechatEnum.getCompanyWechatEnum(corpId).getDesc());
            cmWechatCustRelation.setExternalUserId(relationInfo.getExternalUserId());
            cmWechatCustRelation.setAddWay(relationInfo.getAddWay());
            cmWechatCustRelation.setCreator("sys");
            cmWechatCustRelation.setCreateTime(relationInfo.getCreatetime());
            cmWechatCustRelationMapper.insertSelective(cmWechatCustRelation);
        } else {
            cmWechatCustRelation.setAddTime(relationInfo.getCreatetime());
            cmWechatCustRelation.setUpdateTime(new Date());
            cmWechatCustRelation.setModifier("sys");
            cmWechatCustRelation.setAddWay(relationInfo.getAddWay());
            cmWechatCustRelation.setState(relationInfo.getState());
            cmWechatCustRelationMapper.updateByPrimaryKeySelective(cmWechatCustRelation);
        }

    }

    /**
     * @description:(根据客户id获取所有的投顾客户的添加关系)
     * @param externalIds
     * @param companyNo
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatCustRelationPO>
     * @author: shuai.zhang
     * @date: 2024/3/28 11:28
     * @since JDK 1.8
     */
    public List<CmWechatCustRelationPO> getRealtionListByExternalUserId(ArrayList<String> externalIds, String companyNo) {
        return cmWechatCustRelationMapper.getRealtionListByExternalUserId(externalIds,companyNo);
    }

    /**
     * @description:(旧的投顾客户关系删除)
     * @param info
     * @return int
     * @author: shuai.zhang
     * @date: 2024/3/28 15:27
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int updateCmWechatCustRelationDel(CmWechatCustRelationPO info) {
        return cmWechatCustRelationMapper.updateCmWechatCustRelationDel(info);
    }

    /**
     * @description: 根据外部用户ID、企业微信用户ID、企业ID查询企业微信关系数据
     * @param externalUserId 外部用户ID
     * @param consCodeList 企业微信用户ID列表
     * @param companyNo 企业ID
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatCustRelationPO>
     * @author: hongdong.xie
     * @date: 2024/8/30 18:19
     * @since JDK 1.8
     */
    public List<CmWechatCustRelationPO> getByExternalIdAndUserIdsList(String externalUserId, List<String> consCodeList, String companyNo){
        return customizeCmWechatCustRelationMapper.selectByExternalIdAndUserIdsList(externalUserId, consCodeList, companyNo);
    }

}
