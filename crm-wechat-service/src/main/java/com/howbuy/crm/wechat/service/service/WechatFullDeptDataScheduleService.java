package com.howbuy.crm.wechat.service.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.wechat.dao.po.CmWechatDeptPO;
import com.howbuy.crm.wechat.dao.po.CmWechatEmpPO;
import com.howbuy.crm.wechat.service.business.WechatDataBusiness;
import com.howbuy.crm.wechat.service.repository.CmWechatDeptRepository;
import com.howbuy.crm.wechat.service.repository.CmWechatEmpRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by shucheng on 2022/1/26 16:41
 */
@Slf4j
@Service
@EnableScheduling
@Transactional
public class WechatFullDeptDataScheduleService {

    /** sql的in里每隔 IN_INTERVAL_SIZE 个数进行拆分 */
    @Autowired
    private WechatDataBusiness wechatDataBusiness;
    @Autowired
    private CmWechatDeptRepository cmWechatDeptRepository;
    @Autowired
    private CmWechatEmpRepository cmWechatEmpRepository;

    /**
     * @description:全量处理员工与部门数据
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/27 16:14
     * @since JDK 1.8
     */
    public void execute() {
        //全量比对后插入
        compareInsertAll();
    }

    /**
     * @description:全量插入（全量插入功能停用）
     * @param
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/27 16:14
     * @since JDK 1.8
     */
    @Deprecated
    private void insertAll() {
        // 全量插入部门数据
        List<CmWechatDeptPO> fullDeptList = wechatDataBusiness.getFullDeptList();
        if (CollectionUtils.isEmpty(fullDeptList)) {
            log.info("部门数据全量新增，未获取到数据，不进行处理");
        } else {
            log.info("部门数据全量新增 START，从企业微信总共获取到{}条数据", fullDeptList.size());
            cmWechatDeptRepository.batchInsertWechatDept(fullDeptList);
            log.info("部门数据全量新增 END，{}条数据插入成功", fullDeptList.size());
        }

        // 全量插入员工数据
        List<CmWechatEmpPO> fullDeptUserDetailList = wechatDataBusiness.getFullDeptUserDetailList();
        if (CollectionUtils.isEmpty(fullDeptUserDetailList)) {
            log.info("部门员工数据全量新增，未获取到数据，不进行处理");
        } else {
            log.info("部门员工数据全量新增 START，从企业微信总共获取到{}条数据", fullDeptUserDetailList.size());
            cmWechatEmpRepository.batchInsertWechatEmp(fullDeptUserDetailList);
            log.info("部门员工数据全量新增 END，{}条数据插入成功", fullDeptUserDetailList.size());
        }
    }

    /**
     * @description:全量比对后插入
     * @param
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/27 16:14
     * @since JDK 1.8
     */
    private void compareInsertAll() {
        log.info("部门、员工数据全量比对 START");

        // 处理部门数据
        List<CmWechatDeptPO> fullDeptList = wechatDataBusiness.getFullDeptList();
        if (CollectionUtils.isNotEmpty(fullDeptList)) {
            cmWechatDeptRepository.removeStaleWechatDept(fullDeptList);
            log.info("部门表中过时数据更新");

            // 然后再用全量数据merge到表中数据
            cmWechatDeptRepository.batchMergeWechatDept(fullDeptList);
            log.info("部门表数据merge成功");
        }

        // 处理员工数据
        List<CmWechatEmpPO> fullDeptUserDetailList = wechatDataBusiness.getFullDeptUserDetailList();
        if (CollectionUtils.isNotEmpty(fullDeptUserDetailList)) {
            log.info("compareInsertAll|fullDeptUserDetailList:{}", JSON.toJSONString(fullDeptUserDetailList));
            // 先利用全量数据找出表中未更新状态的离职员工，把表中的离职员工置为删除状态
            cmWechatEmpRepository.removeStaleWechatEmp(fullDeptUserDetailList);
            log.info("员工表中过时数据更新");

            // 然后再用全量数据merge到表中数据
            cmWechatEmpRepository.batchMergeWechatEmp(fullDeptUserDetailList);
            log.info("员工表数据merge成功");
        }

        log.info("部门、员工数据全量比对 END");
    }

}
