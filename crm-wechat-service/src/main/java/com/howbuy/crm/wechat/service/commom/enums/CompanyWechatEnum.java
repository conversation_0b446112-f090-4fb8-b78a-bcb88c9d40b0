package com.howbuy.crm.wechat.service.commom.enums;

import com.howbuy.crm.wechat.service.config.WechatConfig;

import java.util.stream.Stream;

/**
 * @description: 企业微信应用ID
 * @author: yu.zhang
 * @date: 2023/6/12 19:25 
 * @since JDK 1.8
 * @version: 1.0
 */
public enum CompanyWechatEnum {

    WEALTH_COMPANY(WechatConfig.wealthCorpid,"1"),
    FUND_COMPANY(WechatConfig.fundCorpid,"2");

    private final String key;
    private final String desc;

    public static CompanyWechatEnum getCompanyWechatEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        CompanyWechatEnum companyWechatEnum = getCompanyWechatEnum(code);
        return companyWechatEnum == null ? null : companyWechatEnum.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    CompanyWechatEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
