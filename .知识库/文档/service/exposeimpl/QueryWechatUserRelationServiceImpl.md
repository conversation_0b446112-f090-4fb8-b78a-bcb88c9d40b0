# QueryWechatUserRelationServiceImpl 接口详细设计文档

## 1. 接口名称
根据一账通号查询客户与企业微信用户关系接口

## 2. 接口说明
该接口用于根据客户的一账通号查询该客户与企业微信用户（投顾）的关系信息，包括添加时间、删除时间、关系状态等。主要用于CRM系统中客户与投顾关系管理的业务场景，支持查询指定投顾列表或全部投顾的关系信息。

## 3. 接口类型
Dubbo 接口

## 4. 接口地址或方法签名
- **接口类**: `com.howbuy.crm.wechat.client.producer.userrelation.QueryWechatUserRelationService`
- **方法签名**: `Response<QueryWechatUserRelationResponse> execute(QueryWechatUserRelationRequest request)`
- **实现类**: `com.howbuy.crm.wechat.service.exposeimpl.QueryWechatUserRelationServiceImpl`

## 5. 请求参数表

| 中文名 | 英文名（字段名） | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|------------------|------|----------|--------|----------|
| 客户一账通号 | hboneNo | String | 是 | "AFlJ1o2z" | 客户的唯一标识号 |
| 企业微信用户ID列表 | userIdList | List<String> | 否 | ["Ml2k65", "user001"] | 指定查询的企业微信用户ID列表，为空时查询所有关系 |
| 追踪ID | traceId | String | 否 | "7SLXiPM" | 请求追踪标识（继承自BaseRequest） |

## 6. 响应参数表

| 中文名 | 英文名（字段名） | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|------------------|------|----------|--------|----------|
| 返回码 | code | String | 是 | "0000" | 0000-成功，其他为失败码 |
| 返回描述 | description | String | 是 | "成功" | 返回结果描述信息 |
| 返回内容 | returnObject | QueryWechatUserRelationResponse | 否 | - | 具体业务数据 |
| 微信用户昵称 | returnObject.nickName | String | 否 | "张三" | 客户在企业微信中的昵称 |
| 用户关系列表 | returnObject.userRelationList | List<UserRelationVO> | 否 | - | 客户与投顾的关系信息列表 |
| 企业微信用户ID | userRelationList[].userId | String | 否 | "T8cO7mmKXO" | 投顾的企业微信用户ID |
| 微信用户外部ID | userRelationList[].externalUserId | String | 否 | "4zCPZXR" | 客户的外部用户ID |
| 添加好友时间 | userRelationList[].addTime | Date | 否 | "2024-08-30 10:30:00" | 投顾添加客户为好友的时间 |
| 删除好友时间 | userRelationList[].deleteTime | Date | 否 | "2024-09-01 15:20:00" | 删除好友关系的时间 |
| 关系状态 | userRelationList[].status | String | 否 | "1" | 1-新增，2-删除客户，3-被客户删除 |
| 首次添加时间 | userRelationList[].firstAddTime | Date | 否 | "2024-08-30 10:30:00" | 首次建立好友关系的时间 |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 0000 | 成功 | 正常返回数据 |
| ******** | 参数错误 | 一账通号为空或格式错误 |
| ******** | 系统错误 | 系统内部异常 |
| C0510004 | 未查询到数据 | 根据条件未找到相关数据 |

## 8. 关键业务逻辑说明

1. **参数校验**: 首先校验一账通号是否为空，为空则返回参数错误
2. **客户信息查询**: 根据一账通号和公司编号查询客户的企业微信信息（external_user_id、昵称等）
3. **关系数据查询**: 如果客户存在，则根据客户的external_user_id和可选的用户ID列表查询客户与投顾的关系数据
4. **数据组装**: 将查询到的关系数据转换为响应对象，包括用户昵称和关系列表
5. **历史数据兼容**: 对于首次添加时间字段，如果为空则使用创建时间作为默认值
6. **空数据处理**: 如果未查询到客户信息或关系数据，返回空的响应对象而非错误

## 9. 流程图

```plantuml
@startuml
start
:接收请求参数;
:校验一账通号是否为空;
if (一账通号为空?) then (是)
  :返回参数错误;
  stop
endif
:根据一账通号查询客户企微信息;
if (客户信息存在?) then (否)
  :返回空响应对象;
  stop
endif
:根据externalUserId和userIdList查询关系数据;
if (存在关系数据?) then (是)
  :组装响应数据;
  :设置客户昵称;
  :遍历关系数据;
  :转换为UserRelationVO对象;
  :处理首次添加时间默认值;
else (否)
  :返回空关系列表;
endif
:返回成功响应;
stop
@enduml
```

## 10. 时序图

```plantuml
@startuml
participant "客户端" as Client
participant "QueryWechatUserRelationServiceImpl" as Service
participant "WechatCustRelationService" as BusinessService
participant "CmWechatCustInfoRepository" as CustInfoRepo
participant "CmWechatCustRelationRepository" as RelationRepo

Client -> Service: execute(request)
Service -> BusinessService: queryWechatUserRelation(request)
BusinessService -> BusinessService: 校验一账通号
alt 一账通号为空
    BusinessService -> Service: 返回参数错误
    Service -> Client: Response(参数错误)
else 一账通号不为空
    BusinessService -> CustInfoRepo: getExternalUserByHboneNo(hboneNo, companyNo)
    CustInfoRepo -> BusinessService: 返回客户信息
    alt 客户不存在
        BusinessService -> Service: 返回空响应
        Service -> Client: Response(空数据)
    else 客户存在
        BusinessService -> RelationRepo: getByExternalIdAndUserIdsList(externalUserId, userIdList, companyNo)
        RelationRepo -> BusinessService: 返回关系数据列表
        BusinessService -> BusinessService: 组装响应数据
        BusinessService -> Service: 返回成功响应
        Service -> Client: Response(成功数据)
    end
end
@enduml
```

## 11. 异常处理机制

1. **参数校验异常**: 一账通号为空时返回参数错误码 ********
2. **数据库异常**: 数据库连接或查询异常时，通过日志记录错误信息，返回系统错误码 ********
3. **空数据处理**: 未查询到数据时不抛异常，返回空的响应对象，保证接口调用的稳定性
4. **系统异常**: 其他未预期异常通过全局异常处理器捕获，返回系统错误码

## 12. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
|----------|----------|
| WechatCustRelationService | 企业微信客户关系业务服务，处理核心业务逻辑 |
| CmWechatCustInfoRepository | 企业微信客户信息数据访问层，查询客户基本信息 |
| CmWechatCustRelationRepository | 企业微信客户关系数据访问层，查询客户与投顾关系 |
| WechatConfig | 企业微信配置信息，提供公司ID等配置参数 |
| ResponseCodeEnum | 响应码枚举，统一管理接口返回码 |

## 13. 相关数据库表

| 表名 | 说明 |
|------|------|
| cm_wechat_cust_info | 企业微信客户信息表，存储客户基本信息和外部用户ID |
| cm_wechat_cust_relation | 企业微信客户关系表，存储客户与投顾的好友关系信息 |

## 14. 幂等性与安全性说明

- **幂等性**: 该接口为查询接口，天然具备幂等性，多次调用返回相同结果
- **鉴权**: 通过Dubbo服务调用，依赖服务治理框架的安全机制
- **限流**: 可在Dubbo配置中设置调用频率限制
- **验签**: 依赖Dubbo框架的安全传输机制
- **数据安全**: 查询结果不包含敏感信息，仅返回业务关系数据

## 15. 备注与风险点

### 注意事项
1. 一账通号是必传参数，调用方需确保参数有效性
2. userIdList为可选参数，为空时查询所有关系，建议根据实际需要传入以提高查询效率
3. 首次添加时间字段为历史数据兼容处理，新数据会正确记录该字段

### 边界处理
1. 客户不存在时返回空对象而非异常，调用方需判断返回数据
2. 关系数据为空时返回空列表，保证接口稳定性
3. 时间字段可能为空，调用方需做空值判断

### 特殊逻辑说明
1. 企业微信用户ID在响应中对应的是投顾编号（conscode），而非企业微信原始用户ID
2. 状态字段含义：1-新增关系，2-投顾删除客户，3-客户删除投顾
3. 支持按公司维度查询，当前主要用于财富公司的企业微信场景