# QueryWeChatMemberInfoServiceImpl 接口详细设计文档

## 1. 接口名称
企业微信成员信息查询接口

## 2. 接口说明
该接口用于查询企业微信成员的详细信息，包括用户基本信息、头像、二维码等。支持生成带有头像的企业微信二维码图片（Base64格式）。主要用于CRM系统中获取企业微信员工的完整信息，便于客户关系管理和员工信息展示。

**业务背景**：在CRM系统中，需要获取企业微信员工的详细信息用于客户服务、员工管理等场景。

**使用场景**：
- 客户服务人员信息展示
- 员工二维码生成和分享
- 企业微信成员信息同步

## 3. 接口类型
Dubbo 接口

## 4. 接口地址或方法签名
```java
com.howbuy.crm.wechat.client.producer.wechatmembermanagement.QueryWeChatMemberInfoService.queryWeChatMemberInfo(QueryWeChatMemberInfoRequest request)
```

## 5. 请求参数表

| 中文名 | 英文名（字段名） | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|------------------|------|----------|--------|----------|
| 企业微信成员ID | userId | String | 是 | "m2mj" | 企业微信中的成员唯一标识 |
| 是否需要二维码图片 | needQrImageStr | Boolean | 否 | false | 是否需要返回特殊处理的图片：企微头像放置在企微二维码的中间，并把图片转成base64字符串 |

## 6. 响应参数表

| 中文名 | 英文名（字段名） | 类型 | 示例值 | 字段说明 |
|--------|------------------|------|--------|----------|
| 返回码 | code | String | "0000" | 0000-成功 C0510002-参数错误 C0510003-系统错误 C0510004-未查询到数据 |
| 返回描述 | description | String | "ok" | 返回结果描述信息 |
| 返回内容 | returnObject | QueryWeChatMemberInfoResponse | - | 查询结果对象 |
| 用户ID | returnObject.userId | String | "m2mj" | 企业微信成员ID |
| 用户姓名 | returnObject.name | String | "张三" | 成员姓名 |
| 用户邮箱 | returnObject.email | String | "<EMAIL>" | 成员邮箱地址 |
| 个人二维码 | returnObject.qrCode | String | "https://..." | 员工个人二维码，扫描可添加为外部联系人 |
| 手机号 | returnObject.mobile | String | "13800138000" | 成员手机号码 |
| 头像缩略图 | returnObject.thumbAvatar | String | "https://..." | 头像缩略图URL |
| 企微二维码图片 | returnObject.enterpriseWechatQrImageStr | String | "data:image/png;base64,..." | 企微微信二维码base64字符串（将头像缩略图放置到二维码的中间） |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 0000 | 操作成功 | 查询成功 |
| C0510002 | 参数错误 | 请求参数不正确 |
| C0510003 | 系统错误 | 系统内部错误 |
| C0510004 | 未查询到数据 | 没有找到对应的成员信息 |
| C0519999 | 未定义的错误 | 企业微信API返回错误 |

## 8. 关键业务逻辑说明

1. **参数接收**：接收QueryWeChatMemberInfoRequest请求对象，包含userId和needQrImageStr参数
2. **调用企业微信API**：通过WeChatMemberInfoService调用企业微信API获取成员信息
3. **结果校验**：检查返回的JSONObject是否为空，如果为空则返回"没有查询到数据"
4. **错误码判断**：解析企业微信API返回的errcode和errmsg，如果errcode不为"0"则表示调用失败
5. **数据映射**：将企业微信API返回的JSON数据映射到QueryWeChatMemberInfoResponse对象
6. **二维码处理**：如果needQrImageStr为true，则调用QrCodeLogoUtil工具类生成带头像的二维码Base64字符串
7. **异常处理**：二维码生成过程中如果出现异常，记录错误日志但不影响其他数据返回
8. **响应构建**：构建Response对象返回给调用方

## 9. 流程图

```plantuml
@startuml
start
:接收QueryWeChatMemberInfoRequest请求;
:调用WeChatMemberInfoService.queryWeChatMemberInfo();
if (返回结果为空?) then (是)
  :返回"没有查询到数据";
  stop
else (否)
  :解析errcode和errmsg;
  if (errcode != "0"?) then (是)
    :返回错误信息;
    stop
  else (否)
    :创建QueryWeChatMemberInfoResponse对象;
    :映射基本信息(userId, name, email等);
    if (needQrImageStr == true?) then (是)
      :调用QrCodeLogoUtil生成带头像的二维码;
      if (生成成功?) then (是)
        :设置enterpriseWechatQrImageStr;
      else (否)
        :记录错误日志;
      endif
    endif
    :返回成功响应;
    stop
  endif
endif
@enduml
```

## 10. 时序图

```plantuml
@startuml
participant "调用方" as Client
participant "QueryWeChatMemberInfoServiceImpl" as Service
participant "WeChatMemberInfoService" as WechatService
participant "WeChatCommonOuterService" as OuterService
participant "企业微信API" as WechatAPI
participant "QrCodeLogoUtil" as QrUtil

Client -> Service: queryWeChatMemberInfo(request)
Service -> WechatService: queryWeChatMemberInfo(userId)
WechatService -> OuterService: requestInteractRespWithCacheAccessToken()
OuterService -> WechatAPI: GET /user/get
WechatAPI --> OuterService: 返回成员信息JSON
OuterService --> WechatService: 返回JSON字符串
WechatService --> Service: 返回JSONObject

alt 返回结果为空
    Service --> Client: 返回"没有查询到数据"
else 返回结果不为空
    Service -> Service: 检查errcode
    alt errcode != "0"
        Service --> Client: 返回错误信息
    else errcode == "0"
        Service -> Service: 映射响应对象
        alt needQrImageStr == true
            Service -> QrUtil: transferToBase64(qrCode, thumbAvatar)
            QrUtil --> Service: 返回Base64字符串
        end
        Service --> Client: 返回成功响应
    end
end
@enduml
```

## 11. 异常处理机制

### 主要异常场景及处理方式：

1. **企业微信API调用失败**
   - 场景：网络异常、企业微信服务不可用
   - 处理：返回系统错误码，记录错误日志

2. **成员不存在**
   - 场景：传入的userId在企业微信中不存在
   - 处理：返回"没有查询到数据"，不抛出异常

3. **企业微信API返回错误**
   - 场景：企业微信API返回errcode不为"0"
   - 处理：返回对应的错误信息，记录详细日志

4. **二维码生成失败**
   - 场景：图片下载失败、图片处理异常
   - 处理：记录错误日志，但不影响其他数据返回，enterpriseWechatQrImageStr字段为空

5. **参数校验失败**
   - 场景：userId为空或格式不正确
   - 处理：返回参数错误码

## 12. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
|----------|----------|
| WeChatMemberInfoService | 企业微信成员信息查询服务，封装企业微信API调用 |
| WeChatCommonOuterService | 企业微信通用外部服务，处理API请求和Token管理 |
| QrCodeLogoUtil | 二维码工具类，用于生成带头像的二维码图片 |
| ResponseCodeEnum | 响应码枚举，定义统一的返回码 |
| Constants | 常量类，定义企业微信API相关常量 |
| CompanyWechatEnum | 企业微信公司枚举，区分不同企业微信账号 |

## 13. 相关数据库表

| 表名 | 说明 |
|------|------|
| 无 | 该接口直接调用企业微信API，不涉及数据库操作 |

## 14. 幂等性与安全性说明

### 幂等性
- **是否幂等**：是
- **说明**：该接口为查询接口，多次调用相同参数返回相同结果，具有幂等性

### 安全性
- **鉴权**：通过Dubbo接口调用，依赖调用方的鉴权机制
- **限流**：无特殊限流机制，依赖企业微信API的限流策略
- **验签**：无需验签，内部服务调用
- **数据安全**：
  - 不记录敏感信息到日志
  - 企业微信Token通过缓存管理，避免频繁获取
  - 二维码和头像URL为企业微信官方链接，安全可靠

## 15. 备注与风险点

### 注意事项
1. **企业微信Token管理**：依赖WeChatCommonOuterService的Token缓存机制，确保Token有效性
2. **图片处理性能**：二维码生成涉及网络图片下载和图像处理，可能影响响应时间
3. **企业微信API限制**：需要注意企业微信API的调用频率限制

### 边界处理
1. **空值处理**：对企业微信API返回的字段进行空值检查
2. **异常容错**：二维码生成失败不影响基本信息返回
3. **编码处理**：确保中文字符正确编码

### 特殊逻辑说明
1. **Base64前缀**：生成的二维码图片自动添加"data:image/png;base64,"前缀，便于前端直接使用
2. **公司区分**：当前硬编码使用好买财富公司(WEALTH_COMPANY)，后续可能需要支持多公司
3. **可选功能**：二维码生成为可选功能，通过needQrImageStr参数控制，避免不必要的性能开销

### 性能考虑
- 企业微信API调用：约100-300ms
- 二维码生成（如果启用）：约200-500ms
- 总体响应时间：建议控制在1秒以内

---

**文档生成时间**：2025-07-24 14:09:43
**文档版本**：v1.0
**维护人员**：hongdong.xie
