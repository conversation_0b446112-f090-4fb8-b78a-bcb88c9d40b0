# QueryWechatUserRelationServiceImpl 接口设计文档

## 1. 接口名称
根据一账通号查询客户与企业微信用户关系服务

## 2. 接口说明
该接口用于根据客户的一账通号查询该客户与企业微信用户之间的好友关系信息。主要应用于CRM系统中，帮助业务人员了解客户与企业微信员工的联系情况，支持客户关系管理和营销活动的精准投放。

**业务背景：**
- 企业微信作为重要的客户沟通渠道，需要维护客户与员工的关系数据
- 通过一账通号统一标识客户，实现跨系统的客户关系查询
- 支持批量查询特定企业微信用户与客户的关系

**使用场景：**
- 客户经理查看自己与客户的微信好友关系
- 营销活动前筛选有效的客户联系渠道
- 客户关系分析和数据统计

## 3. 接口类型
Dubbo RPC 接口

## 4. 接口地址或方法签名
```java
com.howbuy.crm.wechat.client.producer.userrelation.QueryWechatUserRelationService.execute(QueryWechatUserRelationRequest request)
```

## 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 跟踪ID | traceId | String | 否 | "7SLXiPM" | 请求跟踪标识，用于日志追踪 |
| 客户一账通号 | hboneNo | String | 是 | "AFlJ1o2z" | 客户在好买系统中的唯一标识 |
| 企业微信用户ID列表 | userIdList | List<String> | 否 | ["Ml2k65", "T8cO7mmKXO"] | 指定查询的企业微信用户ID，为空则查询所有 |

## 6. 响应参数表

| 中文名 | 英文名 | 类型 | 示例值 | 字段说明 |
|--------|--------|------|--------|----------|
| 返回码 | code | String | "0000" | 接口调用结果码 |
| 返回描述 | description | String | "成功" | 接口调用结果描述 |
| 返回内容 | returnObject | Object | - | 业务数据对象 |
| 微信用户昵称 | returnObject.nickName | String | "张三" | 客户在微信中的昵称 |
| 用户关系列表 | returnObject.userRelationList | List<UserRelationVO> | - | 企业微信用户好友关系列表 |
| 企业微信用户ID | userRelationList.userId | String | "T8cO7mmKXO" | 企业微信员工用户ID |
| 微信用户外部ID | userRelationList.externalUserId | String | "4zCPZXR" | 客户在企业微信中的外部用户ID |
| 添加好友时间 | userRelationList.addTime | Date | "2024-08-30 10:30:00" | 成为好友的时间 |
| 删除好友时间 | userRelationList.deleteTime | Date | "2024-09-01 15:20:00" | 删除好友的时间（如果有） |
| 关系状态 | userRelationList.status | String | "1" | 1-新增，2-删除客户，3-被客户删除 |
| 首次添加时间 | userRelationList.firstAddTime | Date | "2024-08-30 10:30:00" | 首次建立好友关系的时间 |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 0000 | 成功 | 接口调用成功，返回数据 |
| ******** | 参数错误 | 请求参数校验失败 |
| ******** | 系统错误 | 系统内部异常 |
| ******** | 未查询到数据 | 根据条件未找到相关数据 |

## 8. 关键业务逻辑说明

1. **参数校验**：首先验证必填参数hboneNo是否为空，如果为空则返回参数错误
2. **数据查询**：根据一账通号从数据库中查询客户的基本信息和微信昵称
3. **关系筛选**：如果userIdList不为空，则只查询指定企业微信用户的关系；否则查询所有关系
4. **状态判断**：根据addTime和deleteTime判断当前关系状态
   - 只有addTime：状态为1（新增）
   - 有deleteTime且deleteTime晚于addTime：根据删除类型设置状态2或3
5. **数据组装**：将查询结果组装成响应对象返回
6. **异常处理**：捕获数据库异常和业务异常，返回相应错误码

## 9. 流程图

```plantuml
@startuml
start
:接收请求参数;
:参数校验;
if (hboneNo为空?) then (是)
  :返回参数错误;
  stop
endif
:根据一账通号查询客户信息;
if (客户存在?) then (否)
  :返回未查询到数据;
  stop
endif
:查询客户微信昵称;
if (userIdList不为空?) then (是)
  :按指定用户ID查询关系;
else (否)
  :查询所有用户关系;
endif
:处理关系状态;
:组装响应数据;
:返回成功结果;
stop
@enduml
```

## 10. 时序图

```plantuml
@startuml
participant "调用方" as Client
participant "QueryWechatUserRelationServiceImpl" as Service
participant "WechatCustRelationService" as CustService
participant "数据库" as DB

Client -> Service: execute(request)
activate Service
Service -> Service: 参数校验
Service -> CustService: queryWechatUserRelation(request)
activate CustService
CustService -> DB: 查询客户基本信息
DB --> CustService: 返回客户信息
CustService -> DB: 查询微信昵称
DB --> CustService: 返回昵称信息
CustService -> DB: 查询用户关系列表
DB --> CustService: 返回关系数据
CustService -> CustService: 处理业务逻辑
CustService --> Service: 返回处理结果
deactivate CustService
Service --> Client: 返回响应结果
deactivate Service
@enduml
```

## 11. 异常处理机制

1. **参数异常**：
   - 必填参数为空：返回********参数错误
   - 参数格式不正确：返回********参数错误

2. **业务异常**：
   - 客户不存在：返回********未查询到数据
   - 无关系数据：返回********未查询到数据

3. **系统异常**：
   - 数据库连接异常：返回********系统错误
   - 其他运行时异常：返回********系统错误

4. **日志记录**：所有异常都会记录详细的错误日志，包含traceId便于问题追踪

## 12. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
|----------|----------|
| WechatCustRelationService | 企业微信客户关系服务，负责具体的业务逻辑处理 |
| BaseRequest | 基础请求参数类，提供traceId等公共字段 |
| BaseResponse | 基础响应参数类，提供code、description等公共字段 |
| Response | 统一响应包装类，封装返回结果 |
| Dubbo | 分布式服务框架，提供RPC调用能力 |
| Lombok | 代码生成工具，简化getter/setter等代码 |

## 13. 幂等性与安全性说明

**幂等性**：
- 该接口为查询接口，天然具备幂等性
- 多次调用相同参数返回相同结果
- 不会对系统数据产生副作用

**安全性**：
- 通过Dubbo框架进行内部服务调用，具备基础的网络安全保障
- 建议在网关层进行统一的鉴权和限流控制
- 敏感数据（如客户信息）需要进行脱敏处理
- 建议添加接口调用频率限制，防止恶意查询

**数据安全**：
- 客户一账通号作为敏感信息，需要确保调用方有相应权限
- 返回的客户关系数据应该根据调用方权限进行过滤

## 14. 备注与风险点

**注意事项**：
1. 一账通号必须是有效的客户标识，建议在调用前进行格式校验
2. userIdList为空时会查询所有关系，可能返回大量数据，建议添加分页机制
3. 关系状态的判断逻辑较为复杂，需要确保addTime和deleteTime的准确性

**边界处理**：
1. 客户存在但无微信关系时，返回空的userRelationList
2. 指定的userIdList中包含无效ID时，只返回有效的关系数据
3. 时间字段可能为null，前端需要做好空值处理

**特殊逻辑说明**：
1. firstAddTime字段记录首次建立关系的时间，用于统计分析
2. 删除关系后重新添加，addTime会更新但firstAddTime保持不变
3. status字段的含义需要与前端保持一致，避免理解偏差

**性能考虑**：
1. 建议对频繁查询的客户关系数据进行缓存
2. 大客户的关系数据可能较多，考虑分页查询
3. 数据库查询建议添加适当的索引优化

---
*文档生成时间：2025-07-24 08:43:36*  
*文档版本：v1.0*  
*维护人员：hongdong.xie*