/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.enums;

/**
 * <AUTHOR>
 * @description: 企微ticket类型 "1"-企业ticket "2"-应用ticket
 * @date 2024/9/24 13:21
 * @since JDK 1.8
 */
public enum WechatTicketType {

    /**
     * 企业ticket
     */
    ENTERPRISE_TICKET("1", "企业ticket"),
    /**
     * 应用ticket
     */
    APP_TICKET("2", "应用ticket");

    private String code;

    private String desc;

    WechatTicketType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


}
