/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.producer.wechatmembermanagement.request;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description:  企业微信成员信息查询请求
 * <AUTHOR>
 * @date 2024/6/20 13:18
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryWeChatMemberInfoRequest implements Serializable {

    private static final long serialVersionUID = -4033333928343647660L;
    /**
     * 企业微信成员id
     */
    private String userId;

    /**
     * 是否需要返回特殊处理的图片：企微头像放置在企微二维码的中间，并把图片转成base64字符串
     */
    private Boolean needQrImageStr = false;

}
