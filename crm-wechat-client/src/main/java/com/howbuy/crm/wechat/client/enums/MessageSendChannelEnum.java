package com.howbuy.crm.wechat.client.enums;

/**
 * @description:(消息发送通道|方式， 1-企微群机器发送、 2-企微自建应用发送、3-邮箱、4-短信、5-带附件的邮箱)
 * @return
 * @author: haoran.zhang
 * @date: 2023/10/7 10:51
 * @since JDK 1.8
 */
public enum MessageSendChannelEnum {

    /**
     * 企微群机器发送
     */
    BOT_MESSAGE("1", "企微群机器发送"),
    NEW_APPLICATION_MESSAGE("2", "企微自建应用发送"),

    /**
     * 邮箱发送
     */
    EMAIL("3", "邮箱"),

    /**
     * 短信发送
     */
    SMS("4", "短信"),

    /**
     * 带附件的邮箱
     */
    EMAIL_WITH_ATTACHMENT("5", "带附件的邮箱"),
    ;

    /**
     * 编码
     */
    private String code;


    /**
     * 描述
     */
    private String description;

    MessageSendChannelEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 通过code获得
     *
     * @param code
     *            系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code) {
        MessageSendChannelEnum statusEnum=getEnum(code);
        return statusEnum==null?null :statusEnum.getDescription();
    }


    /**
     * 通过code直接返回 整个枚举类型
     *
     * @param code
     *            系统返回参数编码
     * @return BaseConstantEnum
     */
    public static MessageSendChannelEnum getEnum(String code) {
        for(MessageSendChannelEnum statusEnum : MessageSendChannelEnum.values()){
            if(statusEnum.getCode().equals(code)){
                return statusEnum;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
