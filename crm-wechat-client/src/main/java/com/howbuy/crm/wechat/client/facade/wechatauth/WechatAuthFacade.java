/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.facade.wechatauth;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.domain.request.wechatauth.GetUserIdRequest;
import com.howbuy.crm.wechat.client.domain.response.wechatauth.GetUserIdVO;
import com.howbuy.crm.wechat.client.enums.WechatAppEnum;

/**
 * @description: 微信身份认证接口
 * <AUTHOR>
 * @date 2024/9/19 10:01
 * @since JDK 1.8
 */
public interface WechatAuthFacade {

    /**
     * @api {dubbo} com.howbuy.crm.wechat.client.facade.wechatauth.WechatAuthFacade.getUserId()
     * @apiGroup wechat
     * @apiName getUserId
     * @apiDescription 获取企微用户Id
     * @apiParam (请求体) {String} wechatAppEnumKey 微信应用枚举key {@link WechatAppEnum}
     * @apiParam (请求体) {String} code 通过成员授权获取到的code
     * @apiParamExample 请求体示例
     * {"requestId":"nySdxY0JDC"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.userId 企微用户Id
     * @apiSuccessExample 响应结果示例
     * {"code":"CmN7EIVgUm","data":{"userId":"Pv"},"description":"X4KmnnZDT2"}
     */
    Response<GetUserIdVO> getUserId(GetUserIdRequest request);

}
