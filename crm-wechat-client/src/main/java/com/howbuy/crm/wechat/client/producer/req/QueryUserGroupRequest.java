package com.howbuy.crm.wechat.client.producer.req;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 用户数据查询请求
 * @date 2024/1/19 14:03
 * @since JDK 1.8
 */
@Data
public class QueryUserGroupRequest implements Serializable {

    /**
     * 一账通号
     */
    private String hbOneNo;

    /**
     * 用户微信昵称
     */
    private String wechatNickName;

    /**
     * 部门ID列表
     */
    private List<Integer> deptIdList;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 每页大小
     */
    private Integer pageSize;
}
