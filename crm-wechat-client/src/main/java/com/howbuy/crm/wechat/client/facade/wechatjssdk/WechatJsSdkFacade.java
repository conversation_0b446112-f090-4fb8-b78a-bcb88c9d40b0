/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.facade.wechatjssdk;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.domain.request.wechatjssdk.GetConfigSignatureRequest;
import com.howbuy.crm.wechat.client.domain.response.wechatjssdk.GetConfigSignatureVO;
import com.howbuy.crm.wechat.client.enums.WechatAppEnum;

/**
 * <AUTHOR>
 * @description: 微信JS SDK接口
 * @date 2024/9/19 10:00
 * @since JDK 1.8
 */
public interface WechatJsSdkFacade {

    /**
     * @api {dubbo} com.howbuy.crm.wechat.client.facade.wechatjssdk.WechatJsSdkFacade.getConfigSignature()
     * @apiGroup wechat
     * @apiName getConfigSignature
     * @apiDescription 获取企微配置签名
     * @apiParam (请求体) {String} url url
     * @apiParam (请求体) {String} wechatAppEnumKey 微信应用枚举key {@link WechatAppEnum}
     * @apiParamExample 请求体示例
     * {"requestId":"X84uDxTpR","url":"QvgHvAn"}
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} returnObject 返回内容
     * @apiSuccess (响应结果) {String} returnObject.timestamp 时间戳
     * @apiSuccess (响应结果) {String} returnObject.nonceStr 随机字符
     * @apiSuccess (响应结果) {String} returnObject.signature 签名
     * @apiSuccess (响应结果) {String} returnObject.appTimestamp app-时间戳
     * @apiSuccess (响应结果) {String} returnObject.appNonceStr app-随机字符
     * @apiSuccess (响应结果) {String} returnObject.appSignature app-签名
     * @apiSuccessExample 响应结果示例
     * {"returnObject":{"appNonceStr":"8ERg","signature":"en","appSignature":"7ldq","nonceStr":"Nq","timestamp":"41NPE9s7","appTimestamp":"IqK"},"code":"bPFEo0R","description":"oZm832Z"}
     */
    Response<GetConfigSignatureVO> getConfigSignature(GetConfigSignatureRequest request);

}
