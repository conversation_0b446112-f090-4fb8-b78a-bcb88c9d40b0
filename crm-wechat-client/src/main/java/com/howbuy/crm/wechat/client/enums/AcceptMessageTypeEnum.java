package com.howbuy.crm.wechat.client.enums;

/**
 * @description:(crm接受消息类型： 1-营销喜报、2-机构crm审批发送企微提示、3-企微机器人消息、4-邮箱、5-短信、6-带附件的邮箱)
 * @return
 * @author: haoran.zhang
 * @date: 2023/10/7 10:51
 * @since JDK 1.8
 */
public enum AcceptMessageTypeEnum {

    /**
     * 营销喜报
     */
    MARKETING_REPORT("1", "营销喜报"),
    JG_CRM_CHECK("2", "机构crm审批发送企微提示"),

    ROBOT("3", "企微机器人消息"),


    /**
     * 邮箱发送
     */
    EMAIL("4", "邮箱"),



    /**
     * 短信发送
     */
    SMS("5", "短信"),

    /**
     * 带附件的邮箱
     */
    EMAIL_WITH_ATTACHMENT("6", "带附件的邮箱"),
    ;

    /**
     * 编码
     */
    private String code;


    /**
     * 描述
     */
    private String description;

    AcceptMessageTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 通过code获得
     *
     * @param code
     *            系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code) {
        AcceptMessageTypeEnum statusEnum=getEnum(code);
        return statusEnum==null?null :statusEnum.getDescription();
    }


    /**
     * 通过code直接返回 整个枚举类型
     *
     * @param code
     *            系统返回参数编码
     * @return BaseConstantEnum
     */
    public static AcceptMessageTypeEnum getEnum(String code) {
        for(AcceptMessageTypeEnum statusEnum : AcceptMessageTypeEnum.values()){
            if(statusEnum.getCode().equals(code)){
                return statusEnum;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
