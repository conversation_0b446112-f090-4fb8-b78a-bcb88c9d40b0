/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.producer.userrelation.request;

import com.howbuy.crm.wechat.client.producer.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description: 根据一账通号查询客户与企业微信用户关系请求参数
 * <AUTHOR>
 * @date 2024/8/30 17:47
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryWechatUserRelationRequest extends BaseRequest {
    private static final long serialVersionUID = 1L;
    /**
     * 客户一账通号
     */
    private String hboneNo;
    /**
     * 企业微信用户ID
     */
    private List<String> userIdList;
}