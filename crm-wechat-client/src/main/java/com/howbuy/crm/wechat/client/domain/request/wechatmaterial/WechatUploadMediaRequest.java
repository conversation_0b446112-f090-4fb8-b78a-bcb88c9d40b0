/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.domain.request.wechatmaterial;


import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.wechat.client.enums.WechatAppEnum;
import com.howbuy.crm.wechat.client.enums.WechatUploadTypeEnum;
import com.howbuy.crm.wechat.client.producer.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Description 上传企微媒体文件request
 * @Date 2024/9/12 14:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WechatUploadMediaRequest extends BaseRequest {

    private static final long serialVersionUID = 6531021006760480436L;
    /**
     * 微信应用枚举key {@link WechatAppEnum}
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "微信应用枚举key", isRequired = true)
    private String wechatAppEnumKey;

    /**
     * 文件字节数组
     */
    private byte[] bytes;

    /**
     * 文件类型（图片-image 语音-voice 视频-video 普通文件-file）{@link WechatUploadTypeEnum}
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "文件类型", isRequired = true)
    private String type;

    /**
     * 文件名称
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "文件名称", isRequired = true)
    private String fileName;
}
