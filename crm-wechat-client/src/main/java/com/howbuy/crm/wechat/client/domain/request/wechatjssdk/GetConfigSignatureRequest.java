/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.domain.request.wechatjssdk;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.wechat.client.enums.WechatAppEnum;
import com.howbuy.crm.wechat.client.producer.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/9/4 14:35
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetConfigSignatureRequest extends BaseRequest {

    private static final long serialVersionUID = -42667527783661007L;
    /**
     * 微信应用枚举key {@link WechatAppEnum}
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "微信应用枚举key", isRequired = true)
    private String wechatAppEnumKey;
    /**
     * 待签名url
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "待签名url", isRequired = true)
    private String url;

}
