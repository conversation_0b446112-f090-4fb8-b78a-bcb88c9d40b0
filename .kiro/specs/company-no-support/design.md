# 设计文档

## 概述

本设计文档描述了为CRM微信系统的所有查询接口添加企业编号（companyNo）参数支持的技术方案。通过统一的企业参数支持，实现多企业数据隔离和精确查询功能。设计遵循现有的项目架构规范，确保改造的一致性和可维护性。

## 架构

### 系统架构概述

CRM微信系统采用微服务架构，基于Spring Boot + Dubbo实现，包含以下核心模块：

- **crm-wechat-client**: Dubbo接口定义和出入参定义
- **crm-wechat-service**: 业务逻辑实现和Dubbo接口实现
- **crm-wechat-dao**: 数据库操作和ORM配置
- **crm-wechat-remote**: 公共组件和启动配置

### 企业参数支持架构

```mermaid
graph TB
    A[API调用方] --> B[Dubbo接口层]
    B --> C[参数验证层]
    C --> D[Service业务层]
    D --> E[Repository数据层]
    E --> F[数据库层]
    
    G[企业参数处理器] --> C
    G --> D
    G --> E
    
    H[企业枚举配置] --> G
    I[统一异常处理] --> C
    I --> D
```

### 数据流架构

1. **请求处理流程**：API调用 → 参数验证 → 企业参数处理 → 业务逻辑 → 数据查询 → 结果返回
2. **企业数据隔离**：通过companyNo参数在数据库查询层面实现企业数据隔离
3. **统一处理机制**：所有接口使用统一的企业参数处理逻辑

## 组件和接口

### 接口分类和改造策略

#### 1. Dubbo Facade接口（需要改造）

**WechatAuthFacade（微信认证接口）**
- `getUserId()` - 获取企微用户Id
- 改造方案：在GetUserIdRequest中添加companyNo参数
- 业务影响：需要根据企业区分不同的微信应用配置

**WechatCustInfoFacade（微信客户信息接口）**
- `queryWechatCustInfo()` - 查询微信客户信息
- 改造方案：在QueryWechatCustInfoRequest中添加companyNo参数
- 业务影响：已部分支持，需要完善参数验证和文档

**WechatMaterialFacade（微信素材接口）**
- `uploadMediaFile()` - 上传企微文件
- 改造方案：在WechatUploadMediaRequest中添加companyNo参数
- 业务影响：需要根据企业区分素材存储

**WechatJsSdkFacade（微信JS SDK接口）**
- `getConfigSignature()` - 获取企微配置签名
- 改造方案：在GetConfigSignatureRequest中添加companyNo参数
- 业务影响：需要根据企业区分JS SDK配置

#### 2. Dubbo Producer Service接口（需要改造）

**CmWechatGroupQueryService（微信群查询服务）**
- `queryUserGroupList()` - 用户群数据查询接口
- `getGroupIdByHbOneNo()` - 查询客户当前所在群id接口
- `getJoinGroupResult()` - 查询客户加群结果接口
- `getGroupBreakStatus()` - 查询群是否解散接口
- `queryUserGroupInfoByHbOneNo()` - 用户群数据查询接口
- 改造方案：为所有方法添加companyNo参数支持

**QueryWeChatMemberInfoService（企业微信成员信息服务）**
- `queryWeChatMemberInfo()` - 获取企业微信成员信息接口
- 改造方案：在QueryWeChatMemberInfoRequest中添加companyNo参数

**QueryWechatUserRelationService（用户关系查询服务）**
- `execute()` - 根据一账通号查询客户企微好友关系接口
- 改造方案：在QueryWechatUserRelationRequest中添加companyNo参数

**WechatUserTagService（用户标签服务）**
- `add()` - 新增企业微信用户标签
- `delete()` - 删除企业微信用户标签
- 改造方案：在AddUserTagRequest和DeleteUserTagRequest中添加companyNo参数

#### 3. REST Controller接口（需要改造）

**WechatGroupController**
- `getGroupInfoByUserId()` - 根据用户ID获取群信息
- `getGroupInfoByChatId()` - 根据聊天ID获取群信息
- 改造方案：添加companyNo请求参数

**WechatExternalUserController**
- `getExternalUser()` - 获取外部用户信息（已支持companyNo）
- 改造方案：完善参数验证和文档

**WechatGroupUserController**
- `queryUserGroupList()` - 查询用户群列表
- `getGroupIdByHbOneNo()` - 根据一账通号获取群ID
- `getJoinGroupResult()` - 获取加群结果
- `getGroupBreakStatus()` - 获取群解散状态
- `queryUserGroupInfoByHbOneNo()` - 根据一账通号查询用户群信息
- 改造方案：为所有方法添加companyNo参数支持

**WechatCustRelationController**
- `selectRelationListByVo()` - 查询客户关系列表
- `updateAllCustInfoList()` - 更新所有客户信息列表
- 改造方案：添加companyNo参数支持

**WechatDeptController**
- `executeScheduleService()` - 执行调度服务
- 改造方案：添加companyNo参数支持

#### 4. 消息发送类接口（不支持companyNo）

**SendWechatController（消息发送控制器）**
- `sendVoiceRemind()` - 发送语音提醒
- `querySendStatus()` - 查询发送状态
- `querySendStatusWithMemo()` - 查询发送状态（带备注）
- `sendMessageByIds()` - 根据ID发送消息
- `buildMessageByIds()` - 根据ID构建消息
- 不支持原因：消息发送是跨企业的业务场景，不需要企业隔离

**WechatCallbackController（微信回调控制器）**
- `fundVerify()` - 基金验证回调
- `wealthVerify()` - 财富验证回调
- 不支持原因：回调接口由微信平台调用，不涉及企业参数

**WechatTransferEventDealController（微信转移事件处理控制器）**
- `transferEventDeal()` - 转移事件处理
- `testDfile()` - 测试文件处理
- 不支持原因：事件处理接口，不涉及查询业务

### 核心组件设计

#### 1. 企业参数处理器（CompanyNoHandler）

```java
@Component
public class CompanyNoHandler {
    
    /**
     * 验证企业编号
     */
    public void validateCompanyNo(String companyNo);
    
    /**
     * 获取默认企业编号
     */
    public String getDefaultCompanyNo();
    
    /**
     * 处理企业参数
     */
    public String processCompanyNo(String companyNo);
}
```

#### 2. 统一请求基类扩展

```java
public abstract class BaseCompanyRequest extends BaseRequest {
    /**
     * 企业编号
     */
    private String companyNo;
    
    // getter/setter方法
}
```

#### 3. 企业枚举扩展

```java
public enum CompanyWechatEnum {
    WEALTH("1", "好买财富"),
    FUND("2", "好买基金");
    
    // 枚举实现
}
```

## 数据模型

### 请求参数模型

#### 1. 基础请求类扩展

```java
@Getter
@Setter
public abstract class BaseCompanyRequest extends BaseRequest {
    /**
     * 企业编号（1-好买财富，2-好买基金）
     */
    private String companyNo;
}
```

#### 2. 具体请求类示例

```java
@Getter
@Setter
public class GetUserIdRequest extends BaseCompanyRequest {
    /**
     * 微信应用枚举key
     */
    private String wechatAppEnumKey;
    
    /**
     * 通过成员授权获取到的code
     */
    private String code;
}
```

### 数据库模型

#### 1. 企业字段标准化

所有涉及企业数据的表都应包含`company_no`字段：

```sql
-- 示例表结构
CREATE TABLE cm_wechat_cust_info (
    id BIGINT PRIMARY KEY,
    hbone_no VARCHAR(50),
    external_user_id VARCHAR(100),
    company_no VARCHAR(10) NOT NULL COMMENT '企业编号',
    -- 其他字段
    INDEX idx_company_no (company_no),
    INDEX idx_company_hbone (company_no, hbone_no)
);
```

#### 2. 查询条件标准化

所有查询都应包含企业条件：

```xml
<select id="selectByCondition" parameterType="map" resultMap="BaseResultMap">
    SELECT * FROM table_name
    <where>
        <if test="companyNo != null and companyNo != ''">
            AND company_no = #{companyNo}
        </if>
        <!-- 其他查询条件 -->
    </where>
</select>
```

### 响应数据模型

响应数据模型保持不变，但需要确保返回的数据都属于指定企业：

```java
@Getter
@Setter
public class WechatCustInfoVO {
    /**
     * 一账通号
     */
    private String hboneNo;
    
    /**
     * 企业编码
     */
    private String companyNo;
    
    /**
     * 外部应用用户ID
     */
    private String externalUserId;
    
    // 其他字段
}
```

## 错误处理

### 统一错误码定义

```java
public enum CompanyNoExceptionEnum {
    COMPANY_NO_REQUIRED("C0510001", "企业编号不能为空"),
    COMPANY_NO_INVALID("C0510002", "企业编号无效"),
    COMPANY_NO_NOT_SUPPORTED("C0510003", "该接口不支持企业编号参数"),
    COMPANY_DATA_ACCESS_DENIED("C0510004", "无权访问该企业数据");
}
```

### 异常处理机制

#### 1. 参数验证异常

```java
@Component
public class CompanyNoValidator {
    
    public void validate(String companyNo) {
        if (StringUtils.isEmpty(companyNo)) {
            throw new BusinessException(CompanyNoExceptionEnum.COMPANY_NO_REQUIRED);
        }
        
        if (!CompanyWechatEnum.isValid(companyNo)) {
            throw new BusinessException(CompanyNoExceptionEnum.COMPANY_NO_INVALID);
        }
    }
}
```

#### 2. 数据访问异常

```java
@Aspect
@Component
public class CompanyDataAccessAspect {
    
    @Around("@annotation(CompanyDataAccess)")
    public Object checkCompanyAccess(ProceedingJoinPoint joinPoint) throws Throwable {
        // 检查企业数据访问权限
        // 记录访问日志
        return joinPoint.proceed();
    }
}
```

### 错误响应格式

```json
{
    "code": "C0510002",
    "description": "企业编号无效",
    "returnObject": null,
    "traceId": "trace-id-12345",
    "timestamp": "2025-01-23T10:30:00Z"
}
```

## 测试策略

### 单元测试策略

#### 1. 参数验证测试

```java
@Test
public void testCompanyNoValidation() {
    // 测试空值
    assertThrows(BusinessException.class, () -> {
        companyNoValidator.validate(null);
    });
    
    // 测试无效值
    assertThrows(BusinessException.class, () -> {
        companyNoValidator.validate("invalid");
    });
    
    // 测试有效值
    assertDoesNotThrow(() -> {
        companyNoValidator.validate("1");
    });
}
```

#### 2. 数据隔离测试

```java
@Test
public void testDataIsolation() {
    // 准备测试数据
    setupTestData();
    
    // 测试企业1的数据查询
    List<WechatCustInfoVO> company1Data = wechatCustInfoService.query("1");
    assertThat(company1Data).allMatch(vo -> "1".equals(vo.getCompanyNo()));
    
    // 测试企业2的数据查询
    List<WechatCustInfoVO> company2Data = wechatCustInfoService.query("2");
    assertThat(company2Data).allMatch(vo -> "2".equals(vo.getCompanyNo()));
    
    // 验证数据不重叠
    assertThat(company1Data).doesNotContainAnyElementsOf(company2Data);
}
```

### 集成测试策略

#### 1. 接口测试

```java
@SpringBootTest
@DubboTest
public class CompanyNoIntegrationTest {
    
    @Reference
    private WechatCustInfoFacade wechatCustInfoFacade;
    
    @Test
    public void testQueryWithCompanyNo() {
        QueryWechatCustInfoRequest request = new QueryWechatCustInfoRequest();
        request.setCompanyNo("1");
        request.setExternalUserId("test-external-id");
        
        Response<WechatCustInfoVO> response = wechatCustInfoFacade.queryWechatCustInfo(request);
        
        assertThat(response.getCode()).isEqualTo("0000");
        assertThat(response.getReturnObject().getCompanyNo()).isEqualTo("1");
    }
}
```

#### 2. 端到端测试

```java
@Test
public void testEndToEndDataFlow() {
    // 1. 通过REST接口调用
    String result = restTemplate.postForObject(
        "/wechatcustinfo/query?companyNo=1",
        requestBody,
        String.class
    );
    
    // 2. 验证返回结果
    assertThat(result).contains("\"companyNo\":\"1\"");
    
    // 3. 验证数据库状态
    List<CmWechatCustInfoPO> dbData = custInfoRepository.findByCompanyNo("1");
    assertThat(dbData).isNotEmpty();
}
```

### 性能测试策略

#### 1. 查询性能测试

```java
@Test
public void testQueryPerformance() {
    StopWatch stopWatch = new StopWatch();
    
    stopWatch.start();
    for (int i = 0; i < 1000; i++) {
        wechatCustInfoService.queryByCompanyNo("1");
    }
    stopWatch.stop();
    
    assertThat(stopWatch.getTotalTimeMillis()).isLessThan(5000);
}
```

#### 2. 并发测试

```java
@Test
public void testConcurrentAccess() throws InterruptedException {
    int threadCount = 10;
    CountDownLatch latch = new CountDownLatch(threadCount);
    
    for (int i = 0; i < threadCount; i++) {
        new Thread(() -> {
            try {
                wechatCustInfoService.queryByCompanyNo("1");
            } finally {
                latch.countDown();
            }
        }).start();
    }
    
    assertThat(latch.await(10, TimeUnit.SECONDS)).isTrue();
}
```

## 实施计划

### 阶段1：基础设施准备（1-2天）

1. **创建企业参数处理组件**
   - CompanyNoHandler
   - CompanyNoValidator
   - BaseCompanyRequest基类

2. **扩展异常处理机制**
   - 添加企业相关异常枚举
   - 完善统一异常处理器

3. **准备测试基础设施**
   - 测试数据准备脚本
   - 测试工具类

### 阶段2：Dubbo接口改造（3-4天）

1. **Facade接口改造**
   - WechatAuthFacade
   - WechatCustInfoFacade
   - WechatMaterialFacade
   - WechatJsSdkFacade

2. **Producer Service接口改造**
   - CmWechatGroupQueryService
   - QueryWeChatMemberInfoService
   - QueryWechatUserRelationService
   - WechatUserTagService

3. **请求响应模型更新**
   - 更新所有Request类
   - 完善API文档注释

### 阶段3：REST接口改造（2-3天）

1. **Controller改造**
   - WechatGroupController
   - WechatExternalUserController
   - WechatGroupUserController
   - WechatCustRelationController
   - WechatDeptController

2. **参数验证和处理**
   - 添加参数验证逻辑
   - 统一错误处理

### 阶段4：数据层改造（2-3天）

1. **Repository层改造**
   - 添加企业参数支持
   - 更新查询方法

2. **Mapper层改造**
   - 更新SQL查询条件
   - 添加企业过滤条件

3. **数据库优化**
   - 添加必要的索引
   - 优化查询性能

### 阶段5：测试和文档（2-3天）

1. **单元测试**
   - 参数验证测试
   - 业务逻辑测试
   - 数据隔离测试

2. **集成测试**
   - 接口测试
   - 端到端测试
   - 性能测试

3. **文档更新**
   - API文档更新
   - 接口支持情况清单
   - 使用指南

### 阶段6：部署和验证（1天）

1. **部署准备**
   - 配置检查
   - 数据迁移脚本

2. **生产验证**
   - 功能验证
   - 性能监控
   - 错误监控

## 风险评估和缓解措施

### 技术风险

1. **数据一致性风险**
   - 风险：改造过程中可能出现数据不一致
   - 缓解：分阶段改造，充分测试，保留回滚方案

2. **性能影响风险**
   - 风险：添加企业参数可能影响查询性能
   - 缓解：添加合适的数据库索引，进行性能测试

3. **兼容性风险**
   - 风险：现有调用方可能不兼容新的参数要求
   - 缓解：保持向后兼容，提供默认值处理

### 业务风险

1. **数据隔离失效风险**
   - 风险：企业数据可能被错误访问
   - 缓解：严格的参数验证，完善的权限检查

2. **接口调用失败风险**
   - 风险：参数验证过严可能导致正常调用失败
   - 缓解：合理的参数验证逻辑，详细的错误信息

### 运维风险

1. **监控盲区风险**
   - 风险：新增的企业参数可能缺乏监控
   - 缓解：完善监控指标，添加企业维度的监控

2. **故障排查困难风险**
   - 风险：企业参数增加了故障排查的复杂度
   - 缓解：完善日志记录，添加企业信息到日志中